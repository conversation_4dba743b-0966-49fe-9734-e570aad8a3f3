<?php
// Settings module main file
require_once '../../core/functions.php';

// Check if user is logged in
if (!is_logged_in()) {
    header('Location: ../../index.php?module=auth&action=login');
    exit;
}

$action = $_GET['action'] ?? 'general';

switch ($action) {
    case 'general':
        include 'views/general.php';
        break;
    case 'security':
        include 'views/security.php';
        break;
    case 'notifications':
        include 'views/notifications.php';
        break;
    default:
        include 'views/general.php';
        break;
}
?>

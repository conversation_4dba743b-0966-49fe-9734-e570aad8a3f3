<?php

// core/functions.php

function db_connect() {
    static $pdo;

    if ($pdo) {
        return $pdo;
    }

    $config = require __DIR__ . '/../config/database.php';

    try {
        $pdo = new PDO("{$config['driver']}:{$config['path']}");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $pdo;
    } catch (PDOException $e) {
        die("Database connection failed: " . $e->getMessage());
    }
}

function render($view, $data = []) {
    extract($data);
    ob_start();
    require __DIR__ . "/../{$view}.php";
    $content = ob_get_clean();
    require __DIR__ . "/../templates/layout.php";
}

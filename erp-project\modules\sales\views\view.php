<h2>Order Details #<?php echo $order['id']; ?></h2>

<div>
    <h3>Customer Information</h3>
    <p><strong>Name:</strong> <?php echo htmlspecialchars($order['client_name']); ?></p>
    <p><strong>Email:</strong> <?php echo htmlspecialchars($order['client_email']); ?></p>
    <p><strong>Order Date:</strong> <?php echo htmlspecialchars($order['order_date']); ?></p>
</div>

<h3>Order Items</h3>
<table class="order-items-table">
    <thead>
        <tr>
            <th>Product Name</th>
            <th>SKU</th>
            <th>Quantity</th>
            <th>Price</th>
            <th>Total</th>
        </tr>
    </thead>
    <tbody>
        <?php $grandTotal = 0; ?>
        <?php foreach ($order_items as $item): ?>
            <tr>
                <td><?php echo htmlspecialchars($item['product_name']); ?></td>
                <td><?php echo htmlspecialchars($item['sku']); ?></td>
                <td><?php echo htmlspecialchars($item['quantity']); ?></td>
                <td><?php echo htmlspecialchars(number_format($item['price'], 2)); ?></td>
                <td><?php echo htmlspecialchars(number_format($item['quantity'] * $item['price'], 2)); ?></td>
            </tr>
            <?php $grandTotal += ($item['quantity'] * $item['price']); ?>
        <?php endforeach; ?>
    </tbody>
    <tfoot>
        <tr>
            <td colspan="4" style="text-align: right;"><strong>Grand Total:</strong></td>
            <td><strong><?php echo htmlspecialchars(number_format($grandTotal, 2)); ?></strong></td>
        </tr>
    </tfoot>
</table>

<p><a href="index.php?module=sales&action=list">Back to Orders</a></p>

<?php
// Database schema update script

require_once __DIR__ . '/../core/functions.php';

try {
    $pdo = db_connect();
    
    echo "Updating database schema...\n";
    
    // Drop existing clients table to recreate with new structure
    $pdo->exec("DROP TABLE IF EXISTS clients");
    echo "Dropped existing clients table\n";
    
    // Create new clients table with expanded fields
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS clients (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            email TEXT NOT NULL UNIQUE,
            phone TEXT,
            contact_person TEXT,
            website TEXT,
            industry TEXT,
            address TEXT,
            city TEXT,
            state TEXT,
            postal_code TEXT,
            country TEXT,
            notes TEXT,
            status TEXT DEFAULT 'Active',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ");
    echo "Created new clients table\n";
    
    // Create providers table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS providers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            email TEXT NOT NULL UNIQUE,
            phone TEXT,
            contact_person TEXT,
            website TEXT,
            service_type TEXT,
            address TEXT,
            city TEXT,
            state TEXT,
            postal_code TEXT,
            country TEXT,
            payment_terms TEXT,
            notes TEXT,
            status TEXT DEFAULT 'Active',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ");
    echo "Created providers table\n";
    
    // Insert sample clients
    $pdo->exec("
        INSERT INTO clients (name, email, phone, contact_person, industry, status) VALUES 
        ('Acme Corporation', '<EMAIL>', '******-0123', 'John Smith', 'Technology', 'Active'),
        ('Tech Solutions Inc', '<EMAIL>', '******-0124', 'Jane Doe', 'Technology', 'Active'),
        ('Global Enterprises', '<EMAIL>', '******-0125', 'Bob Johnson', 'Manufacturing', 'Inactive')
    ");
    echo "Inserted sample clients\n";
    
    // Insert sample providers
    $pdo->exec("
        INSERT INTO providers (name, email, phone, contact_person, service_type, status) VALUES 
        ('Office Supplies Co', '<EMAIL>', '******-0200', 'Alice Brown', 'supplies', 'Active'),
        ('Tech Hardware Ltd', '<EMAIL>', '******-0201', 'Mike Wilson', 'technology', 'Active'),
        ('Logistics Partners', '<EMAIL>', '******-0202', 'Sarah Davis', 'logistics', 'Active')
    ");
    echo "Inserted sample providers\n";
    
    echo "Database schema updated successfully!\n";
    
} catch (PDOException $e) {
    echo "Error updating database: " . $e->getMessage() . "\n";
}
?>

<!-- Edit Provider -->
<div class="table-container">
    <div class="table-header">
        <h2 class="table-title">Edit Provider</h2>
        <div class="table-actions">
            <a href="index.php?module=contacts&action=providers" class="button button-outline">
                <i data-lucide="arrow-left"></i>
                <span>Back to Providers</span>
            </a>
        </div>
    </div>
</div>

<!-- Error Messages -->
<?php if (isset($_GET['error'])): ?>
    <div class="alert alert-error">
        <i data-lucide="alert-circle"></i>
        <span>Error updating provider. Please try again.</span>
    </div>
<?php endif; ?>

<!-- Provider Form -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Provider Information</h3>
        <p class="card-description">Update the provider details below</p>
    </div>
    
    <form method="POST" class="card-content">
        <div class="form-grid">
            <div class="form-group">
                <label for="name" class="form-label">Company Name *</label>
                <input type="text" id="name" name="name" class="form-input" required 
                       value="<?php echo htmlspecialchars($provider['name'] ?? ''); ?>"
                       placeholder="Enter company name">
            </div>
            
            <div class="form-group">
                <label for="contact_person" class="form-label">Contact Person</label>
                <input type="text" id="contact_person" name="contact_person" class="form-input" 
                       value="<?php echo htmlspecialchars($provider['contact_person'] ?? ''); ?>"
                       placeholder="Enter contact person name">
            </div>
            
            <div class="form-group">
                <label for="email" class="form-label">Email Address *</label>
                <input type="email" id="email" name="email" class="form-input" required 
                       value="<?php echo htmlspecialchars($provider['email'] ?? ''); ?>"
                       placeholder="Enter email address">
            </div>
            
            <div class="form-group">
                <label for="phone" class="form-label">Phone Number</label>
                <input type="tel" id="phone" name="phone" class="form-input" 
                       value="<?php echo htmlspecialchars($provider['phone'] ?? ''); ?>"
                       placeholder="Enter phone number">
            </div>
            
            <div class="form-group">
                <label for="website" class="form-label">Website</label>
                <input type="url" id="website" name="website" class="form-input" 
                       value="<?php echo htmlspecialchars($provider['website'] ?? ''); ?>"
                       placeholder="https://example.com">
            </div>
            
            <div class="form-group">
                <label for="service_type" class="form-label">Service Type</label>
                <select id="service_type" name="service_type" class="form-select">
                    <option value="">Select service type</option>
                    <option value="supplies" <?php echo ($provider['service_type'] ?? '') === 'supplies' ? 'selected' : ''; ?>>Office Supplies</option>
                    <option value="technology" <?php echo ($provider['service_type'] ?? '') === 'technology' ? 'selected' : ''; ?>>Technology Services</option>
                    <option value="logistics" <?php echo ($provider['service_type'] ?? '') === 'logistics' ? 'selected' : ''; ?>>Logistics & Shipping</option>
                    <option value="consulting" <?php echo ($provider['service_type'] ?? '') === 'consulting' ? 'selected' : ''; ?>>Consulting</option>
                    <option value="manufacturing" <?php echo ($provider['service_type'] ?? '') === 'manufacturing' ? 'selected' : ''; ?>>Manufacturing</option>
                    <option value="maintenance" <?php echo ($provider['service_type'] ?? '') === 'maintenance' ? 'selected' : ''; ?>>Maintenance</option>
                    <option value="other" <?php echo ($provider['service_type'] ?? '') === 'other' ? 'selected' : ''; ?>>Other</option>
                </select>
            </div>
            
            <div class="form-group form-group-full">
                <label for="address" class="form-label">Address</label>
                <textarea id="address" name="address" class="form-textarea" rows="3" 
                          placeholder="Enter full address"><?php echo htmlspecialchars($provider['address'] ?? ''); ?></textarea>
            </div>
            
            <div class="form-group">
                <label for="city" class="form-label">City</label>
                <input type="text" id="city" name="city" class="form-input" 
                       value="<?php echo htmlspecialchars($provider['city'] ?? ''); ?>"
                       placeholder="Enter city">
            </div>
            
            <div class="form-group">
                <label for="state" class="form-label">State/Province</label>
                <input type="text" id="state" name="state" class="form-input" 
                       value="<?php echo htmlspecialchars($provider['state'] ?? ''); ?>"
                       placeholder="Enter state or province">
            </div>
            
            <div class="form-group">
                <label for="postal_code" class="form-label">Postal Code</label>
                <input type="text" id="postal_code" name="postal_code" class="form-input" 
                       value="<?php echo htmlspecialchars($provider['postal_code'] ?? ''); ?>"
                       placeholder="Enter postal code">
            </div>
            
            <div class="form-group">
                <label for="country" class="form-label">Country</label>
                <select id="country" name="country" class="form-select">
                    <option value="">Select country</option>
                    <option value="US" <?php echo ($provider['country'] ?? '') === 'US' ? 'selected' : ''; ?>>United States</option>
                    <option value="CA" <?php echo ($provider['country'] ?? '') === 'CA' ? 'selected' : ''; ?>>Canada</option>
                    <option value="UK" <?php echo ($provider['country'] ?? '') === 'UK' ? 'selected' : ''; ?>>United Kingdom</option>
                    <option value="AU" <?php echo ($provider['country'] ?? '') === 'AU' ? 'selected' : ''; ?>>Australia</option>
                    <option value="DE" <?php echo ($provider['country'] ?? '') === 'DE' ? 'selected' : ''; ?>>Germany</option>
                    <option value="FR" <?php echo ($provider['country'] ?? '') === 'FR' ? 'selected' : ''; ?>>France</option>
                    <option value="other" <?php echo ($provider['country'] ?? '') === 'other' ? 'selected' : ''; ?>>Other</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="payment_terms" class="form-label">Payment Terms</label>
                <select id="payment_terms" name="payment_terms" class="form-select">
                    <option value="">Select payment terms</option>
                    <option value="net_15" <?php echo ($provider['payment_terms'] ?? '') === 'net_15' ? 'selected' : ''; ?>>Net 15</option>
                    <option value="net_30" <?php echo ($provider['payment_terms'] ?? '') === 'net_30' ? 'selected' : ''; ?>>Net 30</option>
                    <option value="net_60" <?php echo ($provider['payment_terms'] ?? '') === 'net_60' ? 'selected' : ''; ?>>Net 60</option>
                    <option value="cod" <?php echo ($provider['payment_terms'] ?? '') === 'cod' ? 'selected' : ''; ?>>Cash on Delivery</option>
                    <option value="prepaid" <?php echo ($provider['payment_terms'] ?? '') === 'prepaid' ? 'selected' : ''; ?>>Prepaid</option>
                </select>
            </div>
            
            <div class="form-group form-group-full">
                <label for="notes" class="form-label">Notes</label>
                <textarea id="notes" name="notes" class="form-textarea" rows="4" 
                          placeholder="Additional notes about this provider"><?php echo htmlspecialchars($provider['notes'] ?? ''); ?></textarea>
            </div>
        </div>
        
        <div class="card-footer">
            <div class="form-actions">
                <a href="index.php?module=contacts&action=providers" class="button button-outline">
                    <i data-lucide="x"></i>
                    <span>Cancel</span>
                </a>
                <button type="submit" class="button">
                    <i data-lucide="save"></i>
                    <span>Update Provider</span>
                </button>
            </div>
        </div>
    </form>
</div>

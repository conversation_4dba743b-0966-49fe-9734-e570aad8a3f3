<!-- Dashboard Stats Grid -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-header">
            <div class="stat-title">Total Users</div>
            <div class="stat-icon">👥</div>
        </div>
        <div class="stat-value"><?php echo $stats['total_users'] ?? '0'; ?></div>
        <div class="stat-change positive">
            <span>↗</span>
            <span>+12% from last month</span>
        </div>
    </div>

    <div class="stat-card">
        <div class="stat-header">
            <div class="stat-title">Total Clients</div>
            <div class="stat-icon">👤</div>
        </div>
        <div class="stat-value"><?php echo $stats['total_clients'] ?? '0'; ?></div>
        <div class="stat-change positive">
            <span>↗</span>
            <span>+8% from last month</span>
        </div>
    </div>

    <div class="stat-card">
        <div class="stat-header">
            <div class="stat-title">Total Products</div>
            <div class="stat-icon">📦</div>
        </div>
        <div class="stat-value"><?php echo $stats['total_products'] ?? '0'; ?></div>
        <div class="stat-change positive">
            <span>↗</span>
            <span>+5% from last month</span>
        </div>
    </div>

    <div class="stat-card">
        <div class="stat-header">
            <div class="stat-title">Total Sales</div>
            <div class="stat-icon">📈</div>
        </div>
        <div class="stat-value">$<?php echo number_format($stats['total_sales'] ?? 0, 2); ?></div>
        <div class="stat-change positive">
            <span>↗</span>
            <span>+15% from last month</span>
        </div>
    </div>
</div>

<!-- Dashboard Content Grid -->
<div style="display: grid; grid-template-columns: 2fr 1fr; gap: var(--spacing-xl); margin-bottom: var(--spacing-xl);">
    <!-- Recent Activity -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Recent Activity</h3>
            <p class="card-description">Latest updates across your system</p>
        </div>
        <div class="card-content">
            <div style="space-y: var(--spacing-md);">
                <?php if (!empty($recent_activity)): ?>
                    <?php foreach ($recent_activity as $activity): ?>
                        <div style="display: flex; align-items: center; gap: var(--spacing-md); padding: var(--spacing-md); border-radius: var(--radius); background: hsl(var(--muted) / 0.3);">
                            <div style="width: 40px; height: 40px; border-radius: 50%; background: var(--gradient-primary); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                                <?php echo substr($activity['type'], 0, 1); ?>
                            </div>
                            <div style="flex: 1;">
                                <div style="font-weight: 500; color: hsl(var(--foreground));"><?php echo htmlspecialchars($activity['description']); ?></div>
                                <div style="font-size: 0.875rem; color: hsl(var(--muted-foreground));"><?php echo $activity['time']; ?></div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div style="text-align: center; padding: var(--spacing-xl); color: hsl(var(--muted-foreground));">
                        <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">📊</div>
                        <p>No recent activity to display</p>
                        <p style="font-size: 0.875rem;">Start by adding users, clients, or products</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Quick Actions</h3>
            <p class="card-description">Common tasks</p>
        </div>
        <div class="card-content">
            <div style="display: flex; flex-direction: column; gap: var(--spacing-md);">
                <a href="index.php?module=users&action=new" class="button button-outline" style="justify-content: flex-start;">
                    <span>👥</span>
                    <span>Add New User</span>
                </a>
                <a href="index.php?module=clients&action=new" class="button button-outline" style="justify-content: flex-start;">
                    <span>👤</span>
                    <span>Add New Client</span>
                </a>
                <a href="index.php?module=products&action=new" class="button button-outline" style="justify-content: flex-start;">
                    <span>📦</span>
                    <span>Add New Product</span>
                </a>
                <a href="index.php?module=sales&action=new" class="button" style="justify-content: flex-start;">
                    <span>📈</span>
                    <span>Create New Sale</span>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Welcome Message -->
<div class="card">
    <div class="card-content">
        <div style="text-align: center; padding: var(--spacing-xl);">
            <h2 style="margin-bottom: var(--spacing-md); background: var(--gradient-primary); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                Welcome to Your ERP Dashboard!
            </h2>
            <p style="font-size: 1.125rem; color: hsl(var(--muted-foreground)); max-width: 600px; margin: 0 auto;">
                Your central hub for managing business operations. Monitor key metrics, track recent activity, and access quick actions to streamline your workflow.
            </p>
        </div>
    </div>
</div>

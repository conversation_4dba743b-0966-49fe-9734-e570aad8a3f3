<!-- Edit Client -->
<div class="table-container">
    <div class="table-header">
        <div class="table-title">
            <h2>Edit Client</h2>
            <p>Update client information</p>
        </div>
        <div class="table-actions">
            <a href="index.php?module=contacts&action=clients" class="button button-outline">
                <i data-lucide="arrow-left"></i>
                <span>Back</span>
            </a>
        </div>
    </div>
</div>

<!-- Error Messages -->
<?php if (isset($_GET['error'])): ?>
    <div class="alert alert-error">
        <i data-lucide="alert-circle"></i>
        <span>Error updating client. Please try again.</span>
    </div>
<?php endif; ?>

<!-- Client Form -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Client Information</h3>
        <p class="card-description">Update client details</p>
    </div>
    
    <form method="POST" class="card-content">
        <div class="form-grid">
            <div class="form-group">
                <label for="name" class="form-label">Company Name *</label>
                <input type="text" id="name" name="name" class="form-input" required 
                       value="<?php echo htmlspecialchars($client['name'] ?? ''); ?>"
                       placeholder="Enter company name">
            </div>
            
            <div class="form-group">
                <label for="contact_person" class="form-label">Contact Person</label>
                <input type="text" id="contact_person" name="contact_person" class="form-input" 
                       value="<?php echo htmlspecialchars($client['contact_person'] ?? ''); ?>"
                       placeholder="Enter contact person name">
            </div>
            
            <div class="form-group">
                <label for="email" class="form-label">Email Address *</label>
                <input type="email" id="email" name="email" class="form-input" required 
                       value="<?php echo htmlspecialchars($client['email'] ?? ''); ?>"
                       placeholder="Enter email address">
            </div>
            
            <div class="form-group">
                <label for="phone" class="form-label">Phone Number</label>
                <input type="tel" id="phone" name="phone" class="form-input" 
                       value="<?php echo htmlspecialchars($client['phone'] ?? ''); ?>"
                       placeholder="Enter phone number">
            </div>
            
            <div class="form-group">
                <label for="website" class="form-label">Website</label>
                <input type="url" id="website" name="website" class="form-input" 
                       value="<?php echo htmlspecialchars($client['website'] ?? ''); ?>"
                       placeholder="https://example.com">
            </div>
            
            <div class="form-group">
                <label for="industry" class="form-label">Industry</label>
                <select id="industry" name="industry" class="form-select">
                    <option value="">Select industry</option>
                    <option value="technology" <?php echo ($client['industry'] ?? '') === 'technology' ? 'selected' : ''; ?>>Technology</option>
                    <option value="healthcare" <?php echo ($client['industry'] ?? '') === 'healthcare' ? 'selected' : ''; ?>>Healthcare</option>
                    <option value="finance" <?php echo ($client['industry'] ?? '') === 'finance' ? 'selected' : ''; ?>>Finance</option>
                    <option value="retail" <?php echo ($client['industry'] ?? '') === 'retail' ? 'selected' : ''; ?>>Retail</option>
                    <option value="manufacturing" <?php echo ($client['industry'] ?? '') === 'manufacturing' ? 'selected' : ''; ?>>Manufacturing</option>
                    <option value="other" <?php echo ($client['industry'] ?? '') === 'other' ? 'selected' : ''; ?>>Other</option>
                </select>
            </div>
            
            <div class="form-group form-group-full">
                <label for="address" class="form-label">Address</label>
                <textarea id="address" name="address" class="form-textarea" rows="3" 
                          placeholder="Enter full address"><?php echo htmlspecialchars($client['address'] ?? ''); ?></textarea>
            </div>
            
            <div class="form-group">
                <label for="city" class="form-label">City</label>
                <input type="text" id="city" name="city" class="form-input" 
                       value="<?php echo htmlspecialchars($client['city'] ?? ''); ?>"
                       placeholder="Enter city">
            </div>
            
            <div class="form-group">
                <label for="state" class="form-label">State/Province</label>
                <input type="text" id="state" name="state" class="form-input" 
                       value="<?php echo htmlspecialchars($client['state'] ?? ''); ?>"
                       placeholder="Enter state or province">
            </div>
            
            <div class="form-group">
                <label for="postal_code" class="form-label">Postal Code</label>
                <input type="text" id="postal_code" name="postal_code" class="form-input" 
                       value="<?php echo htmlspecialchars($client['postal_code'] ?? ''); ?>"
                       placeholder="Enter postal code">
            </div>
            
            <div class="form-group">
                <label for="country" class="form-label">Country</label>
                <select id="country" name="country" class="form-select">
                    <option value="">Select country</option>
                    <option value="US" <?php echo ($client['country'] ?? '') === 'US' ? 'selected' : ''; ?>>United States</option>
                    <option value="CA" <?php echo ($client['country'] ?? '') === 'CA' ? 'selected' : ''; ?>>Canada</option>
                    <option value="UK" <?php echo ($client['country'] ?? '') === 'UK' ? 'selected' : ''; ?>>United Kingdom</option>
                    <option value="AU" <?php echo ($client['country'] ?? '') === 'AU' ? 'selected' : ''; ?>>Australia</option>
                    <option value="DE" <?php echo ($client['country'] ?? '') === 'DE' ? 'selected' : ''; ?>>Germany</option>
                    <option value="FR" <?php echo ($client['country'] ?? '') === 'FR' ? 'selected' : ''; ?>>France</option>
                    <option value="other" <?php echo ($client['country'] ?? '') === 'other' ? 'selected' : ''; ?>>Other</option>
                </select>
            </div>
            
            <div class="form-group form-group-full">
                <label for="notes" class="form-label">Notes</label>
                <textarea id="notes" name="notes" class="form-textarea" rows="4" 
                          placeholder="Additional notes about this client"><?php echo htmlspecialchars($client['notes'] ?? ''); ?></textarea>
            </div>
        </div>
        
        <div class="card-footer">
            <div class="form-actions">
                <a href="index.php?module=contacts&action=clients" class="button button-outline" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px); border: 1px solid hsl(var(--border) / 0.3);">
                    <i data-lucide="arrow-left"></i>
                    <span>Cancel</span>
                </a>
                <button type="submit" class="button" style="background: linear-gradient(135deg, hsl(var(--success)) 0%, hsl(var(--success) / 0.8) 100%); color: white; box-shadow: 0 2px 8px hsl(var(--success) / 0.3); border: none;">
                    <i data-lucide="save"></i>
                    <span>Update Client</span>
                </button>
            </div>
        </div>
    </form>
</div>

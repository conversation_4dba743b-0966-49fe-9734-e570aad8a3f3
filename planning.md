### Project Plan: Modular PHP ERP

The core principle is a "front-controller" pattern with a modular architecture. All requests will go through a single `index.php` file, which will then load the appropriate module to handle the request.

---

#### **Phase 1: The Core Foundation**

This phase is about creating the skeleton of the application. It won't have business features but will provide the essential structure for all future modules.

1.  **Directory Structure:** Create a clean and logical folder layout.
    ```
    /erp-project
    |-- config/
    |   `-- database.php       # Database configuration
    |-- core/
    |   |-- bootstrap.php      # Initializes the application
    |   |-- router.php         # Parses URLs and routes to modules
    |   `-- functions.php      # Global helper functions (e.g., template rendering)
    |-- db/
    |   |-- erp.sqlite         # The SQLite database file
    |   `-- schema.sql         # SQL for creating initial tables
    |-- modules/
    |   `-- (empty for now)    # Each business function will be a module here
    |-- public/
    |   |-- index.php          # The single entry point (Front Controller)
    |   `-- assets/            # For CSS, JS, images
    |-- templates/
    |   |-- header.php         # Common HTML header
    |   |-- footer.php         # Common HTML footer
    |   `-- layout.php         # Main site layout
    `-- .htaccess              # Rewrites all requests to index.php
    ```

2.  **Database Setup:**
    *   Create a `db/schema.sql` file to define the initial tables. We'll start with a `users` table.
    *   Create a core database connection function in `core/functions.php` that returns a persistent PDO instance for the SQLite database.

3.  **Routing:**
    *   The `public/index.php` will be the only PHP file exposed to the web.
    *   It will use a simple URL structure like `index.php?module=users&action=list`.
    *   The `core/router.php` will be responsible for validating the `module` and `action` parameters and including the correct module file.

4.  **Basic Templating:**
    *   Create a `render` function in `core/functions.php` that takes a view name and data as input. It will include `templates/header.php`, the module's specific view file, and `templates/footer.php`.

---

#### **Phase 2: The First Module - User Management**

This will be the first functional part of the ERP, demonstrating the modular concept.

1.  **Module Structure:** Create the user module directory.
    ```
    /modules/users/
    |-- actions.php        # Contains the logic (e.g., list_users, save_user)
    `-- views/
        |-- list.php       # HTML to display a list of users
        `-- form.php       # HTML form to create/edit a user
    ```

2.  **Functionality (CRUD):**
    *   **Create:** A form to add a new user.
    *   **Read:** A page to list all users in a table.
    *   **Update:** A form to edit an existing user's details.
    *   **Delete:** A function to remove a user.

3.  **Implementation:**
    *   The `router.php` will, based on the URL, call a function within `modules/users/actions.php`.
    *   The function in `actions.php` will perform the database operations (e.g., fetch all users from the `users` table).
    *   The action function will then call the `render` function to display the appropriate view from `modules/users/views/`.

---

#### **Phase 3: Expansion - Products & Inventory Module**

This phase proves the extensibility of the architecture by adding a second, independent module.

1.  **Module Structure:** Add a new `products` module following the same pattern.
    ```
    /modules/products/
    |-- actions.php
    `-- views/
        |-- list.php
        `-- form.php
    ```
2.  **Database:** Update `db/schema.sql` with a `products` table (e.g., `id`, `name`, `sku`, `quantity`, `price`).
3.  **Functionality:** Implement the full CRUD operations for managing products, just like with users.

---

#### **Phase 4: Connecting Modules - Sales Orders**

This is a more advanced module that interacts with other modules.

1.  **Module Structure:** Create a `sales` module.
2.  **Database:** Add `orders` and `order_items` tables to the schema. The `orders` table will have a `customer_id` (linking to the `users` table), and `order_items` will link orders to `products`.
3.  **Functionality:**
    *   Create a new order, allowing the user to select a customer and add multiple products.
    *   View a list of all orders.
    *   View the details of a single order, including the customer and products involved.

---

### Technology Summary

*   **Backend:** Native PHP (preferably 8.0+ for modern syntax and features).
*   **Database:** SQLite 3 (via PDO for security and portability).
*   **Frontend:** Plain HTML, CSS, and JavaScript. No frontend frameworks to keep it simple as requested.
*   **Architecture:** Modular Front-Controller.

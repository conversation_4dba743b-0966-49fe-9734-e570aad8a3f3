<?php
// Contacts module actions

function handleContactsAction($action) {
    switch ($action) {
        case 'clients':
            return handleClientsAction();
        case 'providers':
            return handleProvidersAction();
        case 'client_list':
            return handleClientListAction();
        case 'provider_list':
            return handleProviderListAction();
        case 'new_client':
            return handleNewClientAction();
        case 'new_provider':
            return handleNewProviderAction();
        case 'edit_client':
            return handleEditClientAction();
        case 'edit_provider':
            return handleEditProviderAction();
        case 'delete_client':
            return handleDeleteClientAction();
        case 'delete_provider':
            return handleDeleteProviderAction();
        default:
            return handleClientsAction(); // Default to clients view
    }
}

function handleClientsAction() {
    // Get clients data
    $clients = [
        ['id' => 1, 'name' => 'Acme Corporation', 'email' => '<EMAIL>', 'phone' => '******-0123', 'status' => 'Active'],
        ['id' => 2, 'name' => 'Tech Solutions Inc', 'email' => '<EMAIL>', 'phone' => '******-0124', 'status' => 'Active'],
        ['id' => 3, 'name' => 'Global Enterprises', 'email' => '<EMAIL>', 'phone' => '******-0125', 'status' => 'Inactive'],
    ];
    
    return [
        'view' => 'contacts/clients',
        'data' => ['clients' => $clients]
    ];
}

function handleProvidersAction() {
    // Get providers data
    $providers = [
        ['id' => 1, 'name' => 'Office Supplies Co', 'email' => '<EMAIL>', 'phone' => '******-0200', 'status' => 'Active'],
        ['id' => 2, 'name' => 'Tech Hardware Ltd', 'email' => '<EMAIL>', 'phone' => '******-0201', 'status' => 'Active'],
        ['id' => 3, 'name' => 'Logistics Partners', 'email' => '<EMAIL>', 'phone' => '******-0202', 'status' => 'Active'],
    ];
    
    return [
        'view' => 'contacts/providers',
        'data' => ['providers' => $providers]
    ];
}

function handleClientListAction() {
    return handleClientsAction();
}

function handleProviderListAction() {
    return handleProvidersAction();
}

function handleNewClientAction() {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Handle form submission
        $name = $_POST['name'] ?? '';
        $email = $_POST['email'] ?? '';
        $phone = $_POST['phone'] ?? '';
        
        // Here you would save to database
        // For now, just redirect back to clients list
        header('Location: index.php?module=contacts&action=clients&success=1');
        exit;
    }
    
    return [
        'view' => 'contacts/new_client',
        'data' => []
    ];
}

function handleNewProviderAction() {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Handle form submission
        $name = $_POST['name'] ?? '';
        $email = $_POST['email'] ?? '';
        $phone = $_POST['phone'] ?? '';
        
        // Here you would save to database
        // For now, just redirect back to providers list
        header('Location: index.php?module=contacts&action=providers&success=1');
        exit;
    }
    
    return [
        'view' => 'contacts/new_provider',
        'data' => []
    ];
}

function handleEditClientAction() {
    $id = $_GET['id'] ?? 0;
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Handle form submission
        header('Location: index.php?module=contacts&action=clients&updated=1');
        exit;
    }
    
    // Mock client data
    $client = ['id' => $id, 'name' => 'Sample Client', 'email' => '<EMAIL>', 'phone' => '******-0123'];
    
    return [
        'view' => 'contacts/edit_client',
        'data' => ['client' => $client]
    ];
}

function handleEditProviderAction() {
    $id = $_GET['id'] ?? 0;
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Handle form submission
        header('Location: index.php?module=contacts&action=providers&updated=1');
        exit;
    }
    
    // Mock provider data
    $provider = ['id' => $id, 'name' => 'Sample Provider', 'email' => '<EMAIL>', 'phone' => '******-0200'];
    
    return [
        'view' => 'contacts/edit_provider',
        'data' => ['provider' => $provider]
    ];
}

function handleDeleteClientAction() {
    $id = $_GET['id'] ?? 0;
    // Here you would delete from database
    header('Location: index.php?module=contacts&action=clients&deleted=1');
    exit;
}

function handleDeleteProviderAction() {
    $id = $_GET['id'] ?? 0;
    // Here you would delete from database
    header('Location: index.php?module=contacts&action=providers&deleted=1');
    exit;
}
?>

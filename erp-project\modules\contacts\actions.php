<?php
// Contacts module actions

function clients_action() {
    $pdo = db_connect();

    try {
        // Get clients data from database
        $stmt = $pdo->query("SELECT * FROM clients ORDER BY name ASC");
        $clients = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        // If table doesn't exist or error, use empty array
        $clients = [];
        error_log("Database error in clients_action: " . $e->getMessage());
    }

    include __DIR__ . '/../../templates/header.php';
    include __DIR__ . '/views/clients.php';
    include __DIR__ . '/../../templates/footer.php';
}

function providers_action() {
    $pdo = db_connect();

    try {
        // Get providers data from database
        $stmt = $pdo->query("SELECT * FROM providers ORDER BY name ASC");
        $providers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        // If table doesn't exist or error, use empty array
        $providers = [];
        error_log("Database error in providers_action: " . $e->getMessage());
    }

    include __DIR__ . '/../../templates/header.php';
    include __DIR__ . '/views/providers.php';
    include __DIR__ . '/../../templates/footer.php';
}

function new_client_action() {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $pdo = db_connect();

        // Handle form submission
        $name = $_POST['name'] ?? '';
        $email = $_POST['email'] ?? '';
        $phone = $_POST['phone'] ?? '';
        $contact_person = $_POST['contact_person'] ?? '';
        $website = $_POST['website'] ?? '';
        $industry = $_POST['industry'] ?? '';
        $address = $_POST['address'] ?? '';
        $city = $_POST['city'] ?? '';
        $state = $_POST['state'] ?? '';
        $postal_code = $_POST['postal_code'] ?? '';
        $country = $_POST['country'] ?? '';
        $notes = $_POST['notes'] ?? '';

        try {
            $stmt = $pdo->prepare("INSERT INTO clients (name, email, phone, contact_person, website, industry, address, city, state, postal_code, country, notes, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'Active', NOW())");
            $stmt->execute([$name, $email, $phone, $contact_person, $website, $industry, $address, $city, $state, $postal_code, $country, $notes]);

            header('Location: index.php?module=contacts&action=clients&success=1');
            exit;
        } catch (PDOException $e) {
            error_log("Database error in new_client_action: " . $e->getMessage());
            header('Location: index.php?module=contacts&action=new_client&error=1');
            exit;
        }
    }

    include __DIR__ . '/../../templates/header.php';
    include __DIR__ . '/views/new_client.php';
    include __DIR__ . '/../../templates/footer.php';
}

function new_provider_action() {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $pdo = db_connect();

        // Handle form submission
        $name = $_POST['name'] ?? '';
        $email = $_POST['email'] ?? '';
        $phone = $_POST['phone'] ?? '';
        $contact_person = $_POST['contact_person'] ?? '';
        $website = $_POST['website'] ?? '';
        $service_type = $_POST['service_type'] ?? '';
        $address = $_POST['address'] ?? '';
        $city = $_POST['city'] ?? '';
        $state = $_POST['state'] ?? '';
        $postal_code = $_POST['postal_code'] ?? '';
        $country = $_POST['country'] ?? '';
        $payment_terms = $_POST['payment_terms'] ?? '';
        $notes = $_POST['notes'] ?? '';

        try {
            $stmt = $pdo->prepare("INSERT INTO providers (name, email, phone, contact_person, website, service_type, address, city, state, postal_code, country, payment_terms, notes, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'Active', NOW())");
            $stmt->execute([$name, $email, $phone, $contact_person, $website, $service_type, $address, $city, $state, $postal_code, $country, $payment_terms, $notes]);

            header('Location: index.php?module=contacts&action=providers&success=1');
            exit;
        } catch (PDOException $e) {
            error_log("Database error in new_provider_action: " . $e->getMessage());
            header('Location: index.php?module=contacts&action=new_provider&error=1');
            exit;
        }
    }

    include __DIR__ . '/../../templates/header.php';
    include __DIR__ . '/views/new_provider.php';
    include __DIR__ . '/../../templates/footer.php';
}

function edit_client_action() {
    $pdo = db_connect();
    $id = $_GET['id'] ?? 0;

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Handle form submission
        $name = $_POST['name'] ?? '';
        $email = $_POST['email'] ?? '';
        $phone = $_POST['phone'] ?? '';
        $contact_person = $_POST['contact_person'] ?? '';
        $website = $_POST['website'] ?? '';
        $industry = $_POST['industry'] ?? '';
        $address = $_POST['address'] ?? '';
        $city = $_POST['city'] ?? '';
        $state = $_POST['state'] ?? '';
        $postal_code = $_POST['postal_code'] ?? '';
        $country = $_POST['country'] ?? '';
        $notes = $_POST['notes'] ?? '';

        try {
            $stmt = $pdo->prepare("UPDATE clients SET name=?, email=?, phone=?, contact_person=?, website=?, industry=?, address=?, city=?, state=?, postal_code=?, country=?, notes=?, updated_at=NOW() WHERE id=?");
            $stmt->execute([$name, $email, $phone, $contact_person, $website, $industry, $address, $city, $state, $postal_code, $country, $notes, $id]);

            header('Location: index.php?module=contacts&action=clients&updated=1');
            exit;
        } catch (PDOException $e) {
            error_log("Database error in edit_client_action: " . $e->getMessage());
            header('Location: index.php?module=contacts&action=edit_client&id=' . $id . '&error=1');
            exit;
        }
    }

    // Get client data from database
    try {
        $stmt = $pdo->prepare("SELECT * FROM clients WHERE id = ?");
        $stmt->execute([$id]);
        $client = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$client) {
            header('Location: index.php?module=contacts&action=clients&error=not_found');
            exit;
        }
    } catch (PDOException $e) {
        error_log("Database error in edit_client_action: " . $e->getMessage());
        header('Location: index.php?module=contacts&action=clients&error=1');
        exit;
    }

    include __DIR__ . '/../../templates/header.php';
    include __DIR__ . '/views/edit_client.php';
    include __DIR__ . '/../../templates/footer.php';
}

function edit_provider_action() {
    $pdo = db_connect();
    $id = $_GET['id'] ?? 0;

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Handle form submission
        $name = $_POST['name'] ?? '';
        $email = $_POST['email'] ?? '';
        $phone = $_POST['phone'] ?? '';
        $contact_person = $_POST['contact_person'] ?? '';
        $website = $_POST['website'] ?? '';
        $service_type = $_POST['service_type'] ?? '';
        $address = $_POST['address'] ?? '';
        $city = $_POST['city'] ?? '';
        $state = $_POST['state'] ?? '';
        $postal_code = $_POST['postal_code'] ?? '';
        $country = $_POST['country'] ?? '';
        $payment_terms = $_POST['payment_terms'] ?? '';
        $notes = $_POST['notes'] ?? '';

        try {
            $stmt = $pdo->prepare("UPDATE providers SET name=?, email=?, phone=?, contact_person=?, website=?, service_type=?, address=?, city=?, state=?, postal_code=?, country=?, payment_terms=?, notes=?, updated_at=NOW() WHERE id=?");
            $stmt->execute([$name, $email, $phone, $contact_person, $website, $service_type, $address, $city, $state, $postal_code, $country, $payment_terms, $notes, $id]);

            header('Location: index.php?module=contacts&action=providers&updated=1');
            exit;
        } catch (PDOException $e) {
            error_log("Database error in edit_provider_action: " . $e->getMessage());
            header('Location: index.php?module=contacts&action=edit_provider&id=' . $id . '&error=1');
            exit;
        }
    }

    // Get provider data from database
    try {
        $stmt = $pdo->prepare("SELECT * FROM providers WHERE id = ?");
        $stmt->execute([$id]);
        $provider = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$provider) {
            header('Location: index.php?module=contacts&action=providers&error=not_found');
            exit;
        }
    } catch (PDOException $e) {
        error_log("Database error in edit_provider_action: " . $e->getMessage());
        header('Location: index.php?module=contacts&action=providers&error=1');
        exit;
    }

    include __DIR__ . '/../../templates/header.php';
    include __DIR__ . '/views/edit_provider.php';
    include __DIR__ . '/../../templates/footer.php';
}

function delete_client_action() {
    $pdo = db_connect();
    $id = $_GET['id'] ?? 0;

    try {
        $stmt = $pdo->prepare("DELETE FROM clients WHERE id = ?");
        $stmt->execute([$id]);

        header('Location: index.php?module=contacts&action=clients&deleted=1');
        exit;
    } catch (PDOException $e) {
        error_log("Database error in delete_client_action: " . $e->getMessage());
        header('Location: index.php?module=contacts&action=clients&error=1');
        exit;
    }
}

function delete_provider_action() {
    $pdo = db_connect();
    $id = $_GET['id'] ?? 0;

    try {
        $stmt = $pdo->prepare("DELETE FROM providers WHERE id = ?");
        $stmt->execute([$id]);

        header('Location: index.php?module=contacts&action=providers&deleted=1');
        exit;
    } catch (PDOException $e) {
        error_log("Database error in delete_provider_action: " . $e->getMessage());
        header('Location: index.php?module=contacts&action=providers&error=1');
        exit;
    }
}
?>

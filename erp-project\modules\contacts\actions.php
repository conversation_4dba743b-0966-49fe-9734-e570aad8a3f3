<?php
// Contacts module actions

// Include the template system
require_once __DIR__ . '/../../core/template.php';

function clients_action() {
    // Get clients data
    $clients = [
        ['id' => 1, 'name' => 'Acme Corporation', 'email' => '<EMAIL>', 'phone' => '******-0123', 'status' => 'Active'],
        ['id' => 2, 'name' => 'Tech Solutions Inc', 'email' => '<EMAIL>', 'phone' => '******-0124', 'status' => 'Active'],
        ['id' => 3, 'name' => 'Global Enterprises', 'email' => '<EMAIL>', 'phone' => '******-0125', 'status' => 'Inactive'],
    ];

    renderTemplate('contacts/clients', ['clients' => $clients]);
}

function providers_action() {
    // Get providers data
    $providers = [
        ['id' => 1, 'name' => 'Office Supplies Co', 'email' => '<EMAIL>', 'phone' => '******-0200', 'status' => 'Active'],
        ['id' => 2, 'name' => 'Tech Hardware Ltd', 'email' => '<EMAIL>', 'phone' => '******-0201', 'status' => 'Active'],
        ['id' => 3, 'name' => 'Logistics Partners', 'email' => '<EMAIL>', 'phone' => '******-0202', 'status' => 'Active'],
    ];

    renderTemplate('contacts/providers', ['providers' => $providers]);
}

function new_client_action() {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Handle form submission
        $name = $_POST['name'] ?? '';
        $email = $_POST['email'] ?? '';
        $phone = $_POST['phone'] ?? '';

        // Here you would save to database
        // For now, just redirect back to clients list
        header('Location: index.php?module=contacts&action=clients&success=1');
        exit;
    }

    renderTemplate('contacts/new_client', []);
}

function new_provider_action() {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Handle form submission
        $name = $_POST['name'] ?? '';
        $email = $_POST['email'] ?? '';
        $phone = $_POST['phone'] ?? '';

        // Here you would save to database
        // For now, just redirect back to providers list
        header('Location: index.php?module=contacts&action=providers&success=1');
        exit;
    }

    renderTemplate('contacts/new_provider', []);
}

function edit_client_action() {
    $id = $_GET['id'] ?? 0;

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Handle form submission
        header('Location: index.php?module=contacts&action=clients&updated=1');
        exit;
    }

    // Mock client data
    $client = ['id' => $id, 'name' => 'Sample Client', 'email' => '<EMAIL>', 'phone' => '******-0123'];

    renderTemplate('contacts/edit_client', ['client' => $client]);
}

function edit_provider_action() {
    $id = $_GET['id'] ?? 0;

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Handle form submission
        header('Location: index.php?module=contacts&action=providers&updated=1');
        exit;
    }

    // Mock provider data
    $provider = ['id' => $id, 'name' => 'Sample Provider', 'email' => '<EMAIL>', 'phone' => '******-0200'];

    renderTemplate('contacts/edit_provider', ['provider' => $provider]);
}

function delete_client_action() {
    $id = $_GET['id'] ?? 0;
    // Here you would delete from database
    header('Location: index.php?module=contacts&action=clients&deleted=1');
    exit;
}

function delete_provider_action() {
    $id = $_GET['id'] ?? 0;
    // Here you would delete from database
    header('Location: index.php?module=contacts&action=providers&deleted=1');
    exit;
}
?>

<?php
require_once 'erp-project/core/functions.php';

$db = db_connect();

$name = 'Super Admin';
$email = '<EMAIL>';
$password = '$2y$12$9eZQf.Yaobko9NnAO5f6IOKXU.OnRZxH728Ou1lsVduGP9pSeS3vC'; // Hashed password for 'adminpassword'

$stmt = $db->prepare("INSERT INTO users (name, email, password) VALUES (:name, :email, :password)");
$stmt->execute([
    ':name' => $name,
    ':email' => $email,
    ':password' => $password,
]);

echo "Super admin user created successfully!";
?>
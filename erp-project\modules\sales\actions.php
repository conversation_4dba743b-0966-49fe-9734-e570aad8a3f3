<?php

// modules/sales/actions.php

function list_action() {
    $db = db_connect();
    $stmt = $db->query("SELECT o.id, o.order_date, c.name as client_name FROM orders o JOIN clients c ON o.client_id = c.id");
    $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
    render('modules/sales/views/list', ['orders' => $orders]);
}

function new_action() {
    $db = db_connect();
    $clients = $db->query("SELECT id, name FROM clients")->fetchAll(PDO::FETCH_ASSOC);
    $products = $db->query("SELECT id, name, price FROM products")->fetchAll(PDO::FETCH_ASSOC);
    render('modules/sales/views/form', ['clients' => $clients, 'products' => $products]);
}

function save_action() {
    $db = db_connect();
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $db->beginTransaction();
        try {
            // Insert order
            $stmt = $db->prepare("INSERT INTO orders (client_id, order_date) VALUES (:client_id, :order_date)");
            $stmt->execute([
                ':client_id' => $_POST['client_id'],
                ':order_date' => date('Y-m-d H:i:s'),
            ]);
            $order_id = $db->lastInsertId();

            // Insert order items
            foreach ($_POST['product_id'] as $key => $product_id) {
                $stmt = $db->prepare("INSERT INTO order_items (order_id, product_id, quantity, price) VALUES (:order_id, :product_id, :quantity, :price)");
                $stmt->execute([
                    ':order_id' => $order_id,
                    ':product_id' => $product_id,
                    ':quantity' => $_POST['quantity'][$key],
                    ':price' => $_POST['price'][$key],
                ]);
            }
            $db->commit();
        } catch (Exception $e) {
            $db->rollBack();
            die("Error saving order: " . $e->getMessage());
        }
    }
    header('Location: index.php?module=sales&action=list');
}

function view_action() {
    $db = db_connect();
    $order_id = $_GET['id'];

    $stmt = $db->prepare("SELECT o.id, o.order_date, c.name as client_name, c.email as client_email FROM orders o JOIN clients c ON o.client_id = c.id WHERE o.id = :id");
    $stmt->execute([':id' => $order_id]);
    $order = $stmt->fetch(PDO::FETCH_ASSOC);

    $stmt = $db->prepare("SELECT oi.quantity, oi.price, p.name as product_name, p.sku FROM order_items oi JOIN products p ON oi.product_id = p.id WHERE oi.order_id = :order_id");
    $stmt->execute([':order_id' => $order_id]);
    $order_items = $stmt->fetchAll(PDO::FETCH_ASSOC);

    render('modules/sales/views/view', ['order' => $order, 'order_items' => $order_items]);
}

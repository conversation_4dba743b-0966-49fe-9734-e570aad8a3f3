/**
 * Universal UI Components and Functionality
 * Modern ERP System - Enhanced UI Components
 */

// Global UI state
window.ERPComponents = {
    sortDirections: {},
    activeFilters: {},
    bulkSelections: {}
};

/**
 * Initialize enhanced table functionality
 * @param {string} tableId - The table ID
 * @param {Object} options - Configuration options
 */
function initializeEnhancedTable(tableId, options = {}) {
    const defaults = {
        searchInputId: null,
        filterButtonId: null,
        filterDropdownId: null,
        selectAllId: null,
        bulkActionsId: null,
        countElementId: null,
        rowSelector: '.table-row',
        checkboxSelector: '.row-checkbox'
    };
    
    const config = { ...defaults, ...options };
    
    // Initialize search functionality
    if (config.searchInputId) {
        initializeSearch(config);
    }
    
    // Initialize filter functionality
    if (config.filterButtonId && config.filterDropdownId) {
        initializeFilters(config);
    }
    
    // Initialize bulk selection
    if (config.selectAllId && config.bulkActionsId) {
        initializeBulkSelection(config);
    }
}

/**
 * Initialize search functionality
 */
function initializeSearch(config) {
    const searchInput = document.getElementById(config.searchInputId);
    if (!searchInput) return;
    
    searchInput.addEventListener('input', function() {
        filterTableRows(config);
    });
}

/**
 * Initialize filter dropdown functionality
 */
function initializeFilters(config) {
    const filterBtn = document.getElementById(config.filterButtonId);
    const filterDropdown = document.getElementById(config.filterDropdownId);
    const filterOptions = document.querySelectorAll(`#${config.filterDropdownId} .filter-option`);
    
    if (!filterBtn || !filterDropdown) return;
    
    // Toggle dropdown
    filterBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        filterDropdown.classList.toggle('show');
    });
    
    // Close dropdown when clicking outside
    document.addEventListener('click', function() {
        filterDropdown.classList.remove('show');
    });
    
    // Handle filter option clicks
    filterOptions.forEach(option => {
        option.addEventListener('click', function() {
            filterOptions.forEach(opt => opt.classList.remove('active'));
            this.classList.add('active');
            
            window.ERPComponents.activeFilters[config.filterDropdownId] = this.dataset.filter;
            filterBtn.querySelector('span').textContent = this.textContent;
            filterDropdown.classList.remove('show');
            
            filterTableRows(config);
        });
    });
}

/**
 * Initialize bulk selection functionality
 */
function initializeBulkSelection(config) {
    const selectAllCheckbox = document.getElementById(config.selectAllId);
    const rowCheckboxes = document.querySelectorAll(config.checkboxSelector);
    const bulkActions = document.getElementById(config.bulkActionsId);
    
    if (!selectAllCheckbox || !bulkActions) return;
    
    // Select all functionality
    selectAllCheckbox.addEventListener('change', function() {
        const isChecked = this.checked;
        rowCheckboxes.forEach(checkbox => {
            if (checkbox.closest(config.rowSelector).style.display !== 'none') {
                checkbox.checked = isChecked;
            }
        });
        updateBulkActions(config);
    });
    
    // Individual checkbox functionality
    rowCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateBulkActions(config);
            updateSelectAll(config);
        });
    });
}

/**
 * Filter table rows based on search and filters
 */
function filterTableRows(config) {
    const searchInput = document.getElementById(config.searchInputId);
    const rows = document.querySelectorAll(config.rowSelector);
    const countElement = document.getElementById(config.countElementId);
    
    if (!searchInput || !rows.length) return;
    
    const searchTerm = searchInput.value.toLowerCase();
    const currentFilter = window.ERPComponents.activeFilters[config.filterDropdownId] || 'all';
    let visibleCount = 0;
    
    rows.forEach(row => {
        const searchMatch = checkSearchMatch(row, searchTerm);
        const filterMatch = checkFilterMatch(row, currentFilter);
        
        if (searchMatch && filterMatch) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
            const checkbox = row.querySelector(config.checkboxSelector);
            if (checkbox) checkbox.checked = false;
        }
    });
    
    // Update count
    if (countElement) {
        const totalCount = rows.length;
        countElement.textContent = `Showing ${visibleCount} of ${totalCount} items`;
    }
    
    updateBulkActions(config);
    updateSelectAll(config);
}

/**
 * Check if row matches search term
 */
function checkSearchMatch(row, searchTerm) {
    if (!searchTerm) return true;
    
    const searchableData = [
        row.dataset.name || '',
        row.dataset.email || '',
        row.dataset.industry || '',
        row.dataset.serviceType || '',
        row.textContent || ''
    ].join(' ').toLowerCase();
    
    return searchableData.includes(searchTerm);
}

/**
 * Check if row matches current filter
 */
function checkFilterMatch(row, currentFilter) {
    if (!currentFilter || currentFilter === 'all') return true;
    
    // Check status filters
    if (currentFilter === 'active' || currentFilter === 'inactive') {
        return row.dataset.status === currentFilter;
    }
    
    // Check category filters
    return row.dataset.industry === currentFilter || 
           row.dataset.serviceType === currentFilter;
}

/**
 * Update bulk actions visibility
 */
function updateBulkActions(config) {
    const bulkActions = document.getElementById(config.bulkActionsId);
    const checkboxes = document.querySelectorAll(config.checkboxSelector);
    
    if (!bulkActions) return;
    
    const checkedBoxes = Array.from(checkboxes).filter(cb => 
        cb.checked && cb.closest(config.rowSelector).style.display !== 'none'
    );
    
    bulkActions.style.display = checkedBoxes.length > 0 ? 'flex' : 'none';
}

/**
 * Update select all checkbox state
 */
function updateSelectAll(config) {
    const selectAllCheckbox = document.getElementById(config.selectAllId);
    const checkboxes = document.querySelectorAll(config.checkboxSelector);
    
    if (!selectAllCheckbox) return;
    
    const visibleCheckboxes = Array.from(checkboxes).filter(cb => 
        cb.closest(config.rowSelector).style.display !== 'none'
    );
    const checkedVisible = visibleCheckboxes.filter(cb => cb.checked);
    
    selectAllCheckbox.checked = visibleCheckboxes.length > 0 && 
                               checkedVisible.length === visibleCheckboxes.length;
    selectAllCheckbox.indeterminate = checkedVisible.length > 0 && 
                                     checkedVisible.length < visibleCheckboxes.length;
}

/**
 * Generic table sorting function
 */
function sortTable(tableId, column, dataAttribute) {
    const table = document.getElementById(tableId);
    if (!table) return;
    
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr[data-' + dataAttribute + ']'));
    
    // Toggle sort direction
    const sortKey = `${tableId}_${column}`;
    window.ERPComponents.sortDirections[sortKey] = 
        window.ERPComponents.sortDirections[sortKey] === 'asc' ? 'desc' : 'asc';
    
    rows.sort((a, b) => {
        const aVal = a.dataset[dataAttribute] || '';
        const bVal = b.dataset[dataAttribute] || '';
        
        if (window.ERPComponents.sortDirections[sortKey] === 'asc') {
            return aVal.localeCompare(bVal);
        } else {
            return bVal.localeCompare(aVal);
        }
    });
    
    // Re-append sorted rows
    rows.forEach(row => tbody.appendChild(row));
}

/**
 * Generic bulk action handler
 */
function handleBulkAction(action, module, checkboxSelector, confirmMessage) {
    const selectedIds = Array.from(document.querySelectorAll(`${checkboxSelector}:checked`))
        .map(cb => cb.value);
    
    if (selectedIds.length === 0) return;
    
    if (confirm(confirmMessage.replace('{count}', selectedIds.length))) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `index.php?module=${module}&action=${action}`;
        
        selectedIds.forEach(id => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'ids[]';
            input.value = id;
            form.appendChild(input);
        });
        
        document.body.appendChild(form);
        form.submit();
    }
}

/**
 * Enhanced delete confirmation
 */
function confirmDelete(name, deleteUrl) {
    if (confirm(`Are you sure you want to delete "${name}"?\n\nThis action cannot be undone.`)) {
        window.location.href = deleteUrl;
    }
}

/**
 * Initialize tooltips
 */
function initializeTooltips() {
    const tooltipElements = document.querySelectorAll('[title]');
    
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', function() {
            // Add tooltip functionality here if needed
        });
    });
}

// Initialize components when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeTooltips();
    
    // Initialize Lucide icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
});

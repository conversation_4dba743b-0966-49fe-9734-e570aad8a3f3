<div class="card">
    <div class="card-header">
        <h2 class="card-title"><?php echo isset($user) ? 'Edit User' : 'Create New User'; ?></h2>
        <p class="card-description">
            <?php echo isset($user) ? 'Update user information below' : 'Fill in the details to create a new user account'; ?>
        </p>
    </div>

    <div class="card-content">
        <form action="index.php?module=users&action=save" method="post">
            <?php if (isset($user)): ?>
                <input type="hidden" name="id" value="<?php echo $user['id']; ?>">
            <?php endif; ?>

            <div class="form-group">
                <label for="name" class="form-label">Full Name</label>
                <input
                    type="text"
                    id="name"
                    name="name"
                    class="form-input"
                    value="<?php echo isset($user) ? htmlspecialchars($user['name']) : ''; ?>"
                    placeholder="Enter full name"
                    required
                >
                <div class="form-help-text">Enter the user's full name as it should appear in the system</div>
            </div>

            <div class="form-group">
                <label for="email" class="form-label">Email Address</label>
                <input
                    type="email"
                    id="email"
                    name="email"
                    class="form-input"
                    value="<?php echo isset($user) ? htmlspecialchars($user['email']) : ''; ?>"
                    placeholder="<EMAIL>"
                    required
                >
                <div class="form-help-text">This email will be used for login and notifications</div>
            </div>

            <div class="form-group">
                <label for="password" class="form-label">Password</label>
                <input
                    type="password"
                    id="password"
                    name="password"
                    class="form-input"
                    placeholder="Enter password"
                    <?php echo isset($user) ? '' : 'required'; ?>
                >
                <?php if (isset($user)): ?>
                    <div class="form-help-text">Leave blank to keep current password</div>
                <?php else: ?>
                    <div class="form-help-text">Choose a strong password with at least 8 characters</div>
                <?php endif; ?>
            </div>

            <div class="card-footer">
                <div class="flex gap-4">
                    <button type="submit" class="button">
                        <span><?php echo isset($user) ? '💾' : '✨'; ?></span>
                        <span><?php echo isset($user) ? 'Update User' : 'Create User'; ?></span>
                    </button>
                    <a href="index.php?module=users&action=list" class="button button-outline">
                        <span>↩️</span>
                        <span>Back to Users</span>
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

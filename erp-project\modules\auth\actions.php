<?php

// modules/auth/actions.php

function login_action() {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $email = $_POST['email'];
        $password = $_POST['password'];

        $db = db_connect();
        $stmt = $db->prepare("SELECT * FROM users WHERE email = :email");
        $stmt->execute([':email' => $email]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($user && password_verify($password, $user['password'])) {
            session_start();
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_name'] = $user['name'];
            header('Location: index.php?module=dashboard');
            exit();
        } else {
            $error = "Invalid email or password.";
            render('modules/auth/views/login', ['error' => $error]);
        }
    } else {
        render('modules/auth/views/login');
    }
}

function logout_action() {
    session_start();
    session_destroy();
    header('Location: index.php?module=auth&action=login');
    exit();
}

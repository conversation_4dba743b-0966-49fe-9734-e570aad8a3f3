<!-- Add New Provider -->
<div class="content-header">
    <div class="header-top">
        <div class="header-left">
            <h1 class="page-title">Add New Provider</h1>
            <p class="page-description">Create a new provider contact</p>
        </div>
        <div class="header-actions">
            <a href="index.php?module=contacts&action=providers" class="button button-outline">
                <i data-lucide="arrow-left"></i>
                <span>Back to Providers</span>
            </a>
        </div>
    </div>
    
    <!-- Header Sub-menu (Odoo-style) -->
    <div class="header-submenu">
        <a href="index.php?module=contacts&action=clients" class="header-submenu-item">
            <i data-lucide="users"></i>
            <span>Clients</span>
        </a>
        <a href="index.php?module=contacts&action=providers" class="header-submenu-item active">
            <i data-lucide="truck"></i>
            <span>Providers</span>
        </a>
    </div>
</div>

<!-- Provider Form -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Provider Information</h3>
        <p class="card-description">Enter the provider details below</p>
    </div>
    
    <form method="POST" class="card-content">
        <div class="form-grid">
            <div class="form-group">
                <label for="name" class="form-label">Company Name *</label>
                <input type="text" id="name" name="name" class="form-input" required 
                       placeholder="Enter company name">
            </div>
            
            <div class="form-group">
                <label for="contact_person" class="form-label">Contact Person</label>
                <input type="text" id="contact_person" name="contact_person" class="form-input" 
                       placeholder="Enter contact person name">
            </div>
            
            <div class="form-group">
                <label for="email" class="form-label">Email Address *</label>
                <input type="email" id="email" name="email" class="form-input" required 
                       placeholder="Enter email address">
            </div>
            
            <div class="form-group">
                <label for="phone" class="form-label">Phone Number</label>
                <input type="tel" id="phone" name="phone" class="form-input" 
                       placeholder="Enter phone number">
            </div>
            
            <div class="form-group">
                <label for="website" class="form-label">Website</label>
                <input type="url" id="website" name="website" class="form-input" 
                       placeholder="https://example.com">
            </div>
            
            <div class="form-group">
                <label for="service_type" class="form-label">Service Type</label>
                <select id="service_type" name="service_type" class="form-select">
                    <option value="">Select service type</option>
                    <option value="supplies">Office Supplies</option>
                    <option value="technology">Technology Services</option>
                    <option value="logistics">Logistics & Shipping</option>
                    <option value="consulting">Consulting</option>
                    <option value="manufacturing">Manufacturing</option>
                    <option value="maintenance">Maintenance</option>
                    <option value="other">Other</option>
                </select>
            </div>
            
            <div class="form-group form-group-full">
                <label for="address" class="form-label">Address</label>
                <textarea id="address" name="address" class="form-textarea" rows="3" 
                          placeholder="Enter full address"></textarea>
            </div>
            
            <div class="form-group">
                <label for="city" class="form-label">City</label>
                <input type="text" id="city" name="city" class="form-input" 
                       placeholder="Enter city">
            </div>
            
            <div class="form-group">
                <label for="state" class="form-label">State/Province</label>
                <input type="text" id="state" name="state" class="form-input" 
                       placeholder="Enter state or province">
            </div>
            
            <div class="form-group">
                <label for="postal_code" class="form-label">Postal Code</label>
                <input type="text" id="postal_code" name="postal_code" class="form-input" 
                       placeholder="Enter postal code">
            </div>
            
            <div class="form-group">
                <label for="country" class="form-label">Country</label>
                <select id="country" name="country" class="form-select">
                    <option value="">Select country</option>
                    <option value="US">United States</option>
                    <option value="CA">Canada</option>
                    <option value="UK">United Kingdom</option>
                    <option value="AU">Australia</option>
                    <option value="DE">Germany</option>
                    <option value="FR">France</option>
                    <option value="other">Other</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="payment_terms" class="form-label">Payment Terms</label>
                <select id="payment_terms" name="payment_terms" class="form-select">
                    <option value="">Select payment terms</option>
                    <option value="net_15">Net 15</option>
                    <option value="net_30">Net 30</option>
                    <option value="net_60">Net 60</option>
                    <option value="cod">Cash on Delivery</option>
                    <option value="prepaid">Prepaid</option>
                </select>
            </div>
            
            <div class="form-group form-group-full">
                <label for="notes" class="form-label">Notes</label>
                <textarea id="notes" name="notes" class="form-textarea" rows="4" 
                          placeholder="Additional notes about this provider"></textarea>
            </div>
        </div>
        
        <div class="card-footer">
            <div class="form-actions">
                <a href="index.php?module=contacts&action=providers" class="button button-outline">
                    <i data-lucide="x"></i>
                    <span>Cancel</span>
                </a>
                <button type="submit" class="button">
                    <i data-lucide="save"></i>
                    <span>Save Provider</span>
                </button>
            </div>
        </div>
    </form>
</div>

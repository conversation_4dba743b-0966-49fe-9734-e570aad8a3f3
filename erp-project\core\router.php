<?php

// core/router.php

require_once __DIR__ . '/bootstrap.php';
session_start();

$module = $_GET['module'] ?? 'dashboard'; // Default module
$action = $_GET['action'] ?? 'index';     // Default action

// Allow access to login page without authentication
if ($module === 'auth' && $action === 'login') {
    $modulePath = __DIR__ . "/../modules/{$module}/actions.php";
    if (file_exists($modulePath)) {
        require_once $modulePath;
        $functionName = "{$action}_action";
        if (function_exists($functionName)) {
            $functionName();
        } else {
            die("Action '{$action}' not found in module '{$module}'.");
        }
    } else {
        die("Module '{$module}' not found.");
    }
    exit();
}

// Check if user is logged in for all other modules
if (!isset($_SESSION['user_id'])) {
    header('Location: index.php?module=auth&action=login');
    exit();
}

$modulePath = __DIR__ . "/../modules/{$module}/actions.php";

if (file_exists($modulePath)) {
    require_once $modulePath;

    $functionName = "{$action}_action";
    if (function_exists($functionName)) {
        $functionName();
    } else {
        die("Action '{$action}' not found in module '{$module}'.");
    }
} else {
    die("Module '{$module}' not found.");
}

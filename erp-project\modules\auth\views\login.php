<div class="login-container">
    <div class="login-card animate-fade-in">
        <!-- <PERSON>gin Header -->
        <div class="login-header">
            <div class="login-logo">
                🏢
            </div>
            <h1 class="login-title">Welcome Back</h1>
            <p class="login-subtitle">Sign in to your ERP dashboard</p>
        </div>

        <!-- Error Message -->
        <?php if (isset($error)): ?>
            <div class="login-error">
                <span>⚠️</span>
                <span><?php echo htmlspecialchars($error); ?></span>
            </div>
        <?php endif; ?>

        <!-- Login Form -->
        <form class="login-form" action="index.php?module=auth&action=login" method="POST">
            <div class="form-group">
                <label for="email" class="form-label">Email Address</label>
                <input
                    type="email"
                    id="email"
                    name="email"
                    class="login-input"
                    placeholder="Enter your email address"
                    required
                    autocomplete="email"
                    value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>"
                >
            </div>

            <div class="form-group">
                <label for="password" class="form-label">Password</label>
                <input
                    type="password"
                    id="password"
                    name="password"
                    class="login-input"
                    placeholder="Enter your password"
                    required
                    autocomplete="current-password"
                >
            </div>

            <button type="submit" class="login-button">
                <span style="margin-right: var(--spacing-sm);">🔐</span>
                <span>Sign In to Dashboard</span>
            </button>
        </form>

        <!-- Login Footer -->
        <div class="login-footer">
            <span class="login-footer-icon">🔒</span>
            <span>Secured with enterprise-grade encryption</span>
        </div>
    </div>
</div>

<script>
// Add some interactive enhancements to the login page
document.addEventListener('DOMContentLoaded', function() {
    const inputs = document.querySelectorAll('.login-input');
    const button = document.querySelector('.login-button');

    // Add floating label effect
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        input.addEventListener('blur', function() {
            if (!this.value) {
                this.parentElement.classList.remove('focused');
            }
        });

        // Check if input has value on load
        if (input.value) {
            input.parentElement.classList.add('focused');
        }
    });

    // Add loading state to button on form submit
    const form = document.querySelector('.login-form');
    form.addEventListener('submit', function() {
        button.classList.add('loading');
        button.innerHTML = '<span style="margin-right: var(--spacing-sm);">⏳</span><span>Signing In...</span>';
        button.disabled = true;
    });

    // Auto-focus first input
    const firstInput = document.getElementById('email');
    if (firstInput && !firstInput.value) {
        setTimeout(() => firstInput.focus(), 100);
    }
});
</script>
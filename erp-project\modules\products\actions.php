<?php

// modules/products/actions.php

function list_action() {
    $db = db_connect();
    $stmt = $db->query("SELECT * FROM products");
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    render('modules/products/views/list', ['products' => $products]);
}

function new_action() {
    render('modules/products/views/form');
}

function save_action() {
    $db = db_connect();
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['id']) && !empty($_POST['id'])) {
            // Update existing product
            $stmt = $db->prepare("UPDATE products SET name = :name, sku = :sku, quantity = :quantity, price = :price WHERE id = :id");
            $stmt->execute([
                ':name' => $_POST['name'],
                ':sku' => $_POST['sku'],
                ':quantity' => $_POST['quantity'],
                ':price' => $_POST['price'],
                ':id' => $_POST['id'],
            ]);
        } else {
            // Create new product
            $stmt = $db->prepare("INSERT INTO products (name, sku, quantity, price) VALUES (:name, :sku, :quantity, :price)");
            $stmt->execute([
                ':name' => $_POST['name'],
                ':sku' => $_POST['sku'],
                ':quantity' => $_POST['quantity'],
                ':price' => $_POST['price'],
            ]);
        }
    }
    header('Location: index.php?module=products&action=list');
}

function edit_action() {
    $db = db_connect();
    $stmt = $db->prepare("SELECT * FROM products WHERE id = :id");
    $stmt->execute([':id' => $_GET['id']]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);
    render('modules/products/views/form', ['product' => $product]);
}

function delete_action() {
    $db = db_connect();
    $stmt = $db->prepare("DELETE FROM products WHERE id = :id");
    $stmt->execute([':id' => $_GET['id']]);
    header('Location: index.php?module=products&action=list');
}

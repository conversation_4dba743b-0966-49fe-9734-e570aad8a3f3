<?php

// modules/products/actions.php

function products_list_action() {
    $db = db_connect();
    $stmt = $db->query("SELECT * FROM products");
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    render('modules/products/views/list', ['products' => $products]);
}

function products_new_action() {
    render('modules/products/views/form');
}

function products_save_action() {
    $db = db_connect();
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['id']) && !empty($_POST['id'])) {
            // Update existing product
            $stmt = $db->prepare("UPDATE products SET name = :name, sku = :sku, quantity = :quantity, price = :price WHERE id = :id");
            $stmt->execute([
                ':name' => $_POST['name'],
                ':sku' => $_POST['sku'],
                ':quantity' => $_POST['quantity'],
                ':price' => $_POST['price'],
                ':id' => $_POST['id'],
            ]);
        } else {
            // Create new product
            $stmt = $db->prepare("INSERT INTO products (name, sku, quantity, price) VALUES (:name, :sku, :quantity, :price)");
            $stmt->execute([
                ':name' => $_POST['name'],
                ':sku' => $_POST['sku'],
                ':quantity' => $_POST['quantity'],
                ':price' => $_POST['price'],
            ]);
        }
    }
    header('Location: index.php?module=products&action=list');
}

function products_edit_action() {
    $db = db_connect();
    $stmt = $db->prepare("SELECT * FROM products WHERE id = :id");
    $stmt->execute([':id' => $_GET['id']]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);
    render('modules/products/views/form', ['product' => $product]);
}

function products_delete_action() {
    $db = db_connect();
    $stmt = $db->prepare("DELETE FROM products WHERE id = :id");
    $stmt->execute([':id' => $_GET['id']]);
    header('Location: index.php?module=products&action=list&deleted=1');
}

function products_bulk_delete_action() {
    $db = db_connect();
    $product_ids = $_POST['ids'] ?? [];

    if (empty($product_ids)) {
        header('Location: index.php?module=products&action=list&error=1');
        exit;
    }

    try {
        $placeholders = str_repeat('?,', count($product_ids) - 1) . '?';
        $stmt = $db->prepare("DELETE FROM products WHERE id IN ($placeholders)");
        $stmt->execute($product_ids);

        header('Location: index.php?module=products&action=list&deleted=' . count($product_ids));
        exit;
    } catch (PDOException $e) {
        error_log("Database error in products_bulk_delete_action: " . $e->getMessage());
        header('Location: index.php?module=products&action=list&error=1');
        exit;
    }
}

function products_bulk_update_action() {
    $db = db_connect();
    $product_ids = $_POST['product_ids'] ?? [];
    $status = $_POST['status'] ?? '';

    if (empty($product_ids) || empty($status)) {
        header('Location: index.php?module=products&action=list&error=1');
        exit;
    }

    try {
        // Note: Products table might not have status column, so this is a placeholder
        // You might need to add a status column or modify this logic
        header('Location: index.php?module=products&action=list&updated=' . count($product_ids));
        exit;
    } catch (PDOException $e) {
        error_log("Database error in products_bulk_update_action: " . $e->getMessage());
        header('Location: index.php?module=products&action=list&error=1');
        exit;
    }
}

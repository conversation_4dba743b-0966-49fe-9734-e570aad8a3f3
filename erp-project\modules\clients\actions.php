<?php

// modules/clients/actions.php

function list_action() {
    $db = db_connect();
    $stmt = $db->query("SELECT * FROM clients");
    $clients = $stmt->fetchAll(PDO::FETCH_ASSOC);
    render('modules/clients/views/list', ['clients' => $clients]);
}

function new_action() {
    render('modules/clients/views/form');
}

function save_action() {
    $db = db_connect();
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $name = $_POST['name'];
        $email = $_POST['email'];
        $phone = $_POST['phone'] ?? '';
        $address = $_POST['address'] ?? '';

        if (isset($_POST['id']) && !empty($_POST['id'])) {
            // Update existing client
            $stmt = $db->prepare("UPDATE clients SET name = :name, email = :email, phone = :phone, address = :address WHERE id = :id");
            $stmt->execute([
                ':name' => $name,
                ':email' => $email,
                ':phone' => $phone,
                ':address' => $address,
                ':id' => $_POST['id'],
            ]);
        } else {
            // Create new client
            $stmt = $db->prepare("INSERT INTO clients (name, email, phone, address) VALUES (:name, :email, :phone, :address)");
            $stmt->execute([
                ':name' => $name,
                ':email' => $email,
                ':phone' => $phone,
                ':address' => $address,
            ]);
        }
    }
    header('Location: index.php?module=clients&action=list');
    exit();
}

function edit_action() {
    $db = db_connect();
    $stmt = $db->prepare("SELECT * FROM clients WHERE id = :id");
    $stmt->execute([':id' => $_GET['id']]);
    $client = $stmt->fetch(PDO::FETCH_ASSOC);
    render('modules/clients/views/form', ['client' => $client]);
}

function delete_action() {
    $db = db_connect();
    $stmt = $db->prepare("DELETE FROM clients WHERE id = :id");
    $stmt->execute([':id' => $_GET['id']]);
    header('Location: index.php?module=clients&action=list');
    exit();
}

<div class="card">
    <div class="card-header">
        <h2 class="card-title"><?php echo isset($client) ? 'Edit Client' : 'Create New Client'; ?></h2>
        <p class="card-description">
            <?php echo isset($client) ? 'Update client information below' : 'Fill in the details to add a new client to your system'; ?>
        </p>
    </div>

    <div class="card-content">
        <form action="index.php?module=clients&action=save" method="post">
            <?php if (isset($client)): ?>
                <input type="hidden" name="id" value="<?php echo $client['id']; ?>">
            <?php endif; ?>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-md); margin-bottom: var(--spacing-md);">
                <div class="form-group">
                    <label for="name" class="form-label">Client Name</label>
                    <input
                        type="text"
                        id="name"
                        name="name"
                        class="form-input"
                        value="<?php echo isset($client) ? htmlspecialchars($client['name']) : ''; ?>"
                        placeholder="Enter client name"
                        required
                    >
                    <div class="form-help-text">Full name or company name</div>
                </div>

                <div class="form-group">
                    <label for="email" class="form-label">Email Address</label>
                    <input
                        type="email"
                        id="email"
                        name="email"
                        class="form-input"
                        value="<?php echo isset($client) ? htmlspecialchars($client['email']) : ''; ?>"
                        placeholder="<EMAIL>"
                        required
                    >
                    <div class="form-help-text">Primary contact email</div>
                </div>
            </div>

            <div class="form-group">
                <label for="phone" class="form-label">Phone Number</label>
                <input
                    type="text"
                    id="phone"
                    name="phone"
                    class="form-input"
                    value="<?php echo isset($client) ? htmlspecialchars($client['phone']) : ''; ?>"
                    placeholder="+****************"
                >
                <div class="form-help-text">Contact phone number (optional)</div>
            </div>

            <div class="form-group">
                <label for="address" class="form-label">Address</label>
                <textarea
                    id="address"
                    name="address"
                    class="form-textarea"
                    rows="4"
                    placeholder="Enter full address including street, city, state, and postal code"
                ><?php echo isset($client) ? htmlspecialchars($client['address']) : ''; ?></textarea>
                <div class="form-help-text">Complete mailing address (optional)</div>
            </div>

            <div class="card-footer">
                <div class="flex gap-4">
                    <button type="submit" class="button">
                        <span><?php echo isset($client) ? '💾' : '✨'; ?></span>
                        <span><?php echo isset($client) ? 'Update Client' : 'Create Client'; ?></span>
                    </button>
                    <a href="index.php?module=clients&action=list" class="button button-outline">
                        <span>↩️</span>
                        <span>Back to Clients</span>
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

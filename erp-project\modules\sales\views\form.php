<h2>Create New Order</h2>

<form action="index.php?module=sales&action=save" method="post">
    <div>
        <label for="client_id">Client:</label>
        <select id="client_id" name="client_id" required>
            <option value="">Select a client</option>
            <?php foreach ($clients as $client): ?>
                <option value="<?php echo $client['id']; ?>"><?php echo htmlspecialchars($client['name']); ?></option>
            <?php endforeach; ?>
        </select>
    </div>

    <h3>Order Items</h3>
    <div id="order-items">
        <div class="order-item">
            <div>
                <label for="product_id">Product:</label>
                <select name="product_id[]" class="product-select" required>
                    <option value="">Select a product</option>
                    <?php foreach ($products as $product): ?>
                        <option value="<?php echo $product['id']; ?>" data-price="<?php echo $product['price']; ?>"><?php echo htmlspecialchars($product['name']); ?> (<?php echo htmlspecialchars(number_format($product['price'], 2)); ?>)</option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div>
                <label for="quantity">Quantity:</label>
                <input type="number" name="quantity[]" value="1" min="1" class="quantity-input" required>
            </div>
            <div>
                <label for="price">Price:</label>
                <input type="number" name="price[]" step="0.01" class="price-input" readonly>
            </div>
            <button type="button" class="remove-item">Remove</button>
        </div>
    </div>
    <button type="button" class="button button-secondary" id="add-item">Add Item</button>

    <button type="submit" class="button">Save Order</button>
</form>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const orderItemsContainer = document.getElementById('order-items');
        const addItemButton = document.getElementById('add-item');

        function updatePrice(itemDiv) {
            const productSelect = itemDiv.querySelector('.product-select');
            const priceInput = itemDiv.querySelector('.price-input');
            const selectedOption = productSelect.options[productSelect.selectedIndex];
            const price = selectedOption.dataset.price;
            priceInput.value = price;
        }

        orderItemsContainer.addEventListener('change', function(event) {
            if (event.target.classList.contains('product-select')) {
                updatePrice(event.target.closest('.order-item'));
            }
        });

        addItemButton.addEventListener('click', function() {
            const newItem = document.querySelector('.order-item').cloneNode(true);
            newItem.querySelectorAll('input, select').forEach(input => input.value = '');
            newItem.querySelector('.quantity-input').value = '1';
            newItem.querySelector('.price-input').value = '';
            orderItemsContainer.appendChild(newItem);
        });

        orderItemsContainer.addEventListener('click', function(event) {
            if (event.target.classList.contains('remove-item')) {
                if (orderItemsContainer.children.length > 1) {
                    event.target.closest('.order-item').remove();
                } else {
                    alert('You must have at least one item in the order.');
                }
            }
        });

        // Initialize prices for existing items (if any, on edit)
        orderItemsContainer.querySelectorAll('.order-item').forEach(updatePrice);
    });
</script>

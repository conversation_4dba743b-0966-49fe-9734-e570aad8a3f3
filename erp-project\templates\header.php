<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ERP System</title>
    <link rel="stylesheet" href="assets/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
</head>
<body>
    <!-- Dark Mode Toggle -->
    <button class="theme-toggle" onclick="toggleTheme()" title="Toggle dark mode">
        <i id="theme-icon" data-lucide="moon"></i>
    </button>



    <?php $current_module = $_GET['module'] ?? 'dashboard'; ?>

    <!-- App Menu Button -->
    <button class="app-menu-button" onclick="toggleAppMenu()" title="Open app menu">
        <i data-lucide="grid-3x3"></i>
    </button>

    <!-- Floating App Menu -->
    <div class="app-menu-overlay" id="app-menu-overlay" onclick="closeAppMenu()"></div>
    <div class="app-menu" id="app-menu">
        <div class="app-menu-header">
            <h3>Applications</h3>
            <button class="app-menu-close" onclick="closeAppMenu()">×</button>
        </div>
        <div class="app-menu-grid">
            <a href="index.php?module=dashboard" class="app-menu-item <?php echo ($current_module === 'dashboard') ? 'active' : ''; ?>">
                <div class="app-icon dashboard">
                    <i data-lucide="layout-dashboard"></i>
                </div>
                <span class="app-name">Dashboard</span>
            </a>
            <a href="index.php?module=settings&action=general" class="app-menu-item <?php echo ($current_module === 'settings' || $current_module === 'users') ? 'active' : ''; ?>">
                <div class="app-icon settings">
                    <i data-lucide="settings"></i>
                </div>
                <span class="app-name">Settings</span>
            </a>
            <a href="index.php?module=contacts&action=clients" class="app-menu-item <?php echo ($current_module === 'contacts') ? 'active' : ''; ?>">
                <div class="app-icon contacts">
                    <i data-lucide="contact"></i>
                </div>
                <span class="app-name">Contacts</span>
            </a>
            <a href="index.php?module=products&action=list" class="app-menu-item <?php echo ($current_module === 'products') ? 'active' : ''; ?>">
                <div class="app-icon products">
                    <i data-lucide="package"></i>
                </div>
                <span class="app-name">Products</span>
            </a>
            <a href="index.php?module=sales&action=list" class="app-menu-item <?php echo ($current_module === 'sales') ? 'active' : ''; ?>">
                <div class="app-icon sales">
                    <i data-lucide="trending-up"></i>
                </div>
                <span class="app-name">Sales</span>
            </a>
            <a href="index.php?module=auth&action=logout" class="app-menu-item">
                <div class="app-icon logout">
                    <i data-lucide="log-out"></i>
                </div>
                <span class="app-name">Logout</span>
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content" id="main-content">
            <header class="content-header">
                <div class="header-top">
                    <div class="header-title-section">
                        <?php
                        // Breadcrumb navigation
                        $breadcrumbs = [];
                        $action = $_GET['action'] ?? 'list';

                        switch ($current_module) {
                            case 'dashboard':
                                $breadcrumbs = ['Dashboard'];
                                break;
                            case 'users':
                                $breadcrumbs = ['Settings', 'Users'];
                                break;
                            case 'contacts':
                                $breadcrumbs = ['Contacts'];
                                if ($action === 'clients') $breadcrumbs[] = 'Clients';
                                elseif ($action === 'providers') $breadcrumbs[] = 'Providers';
                                break;
                            case 'products':
                                $breadcrumbs = ['Products'];
                                break;
                            case 'sales':
                                $breadcrumbs = ['Sales'];
                                break;
                            case 'settings':
                                $breadcrumbs = ['Settings'];
                                if ($action === 'general') $breadcrumbs[] = 'General';
                                elseif ($action === 'security') $breadcrumbs[] = 'Security';
                                elseif ($action === 'notifications') $breadcrumbs[] = 'Notifications';
                                break;
                            default:
                                $breadcrumbs = ['ERP System'];
                        }
                        ?>

                        <nav class="breadcrumb">
                            <?php foreach ($breadcrumbs as $index => $crumb): ?>
                                <?php if ($index > 0): ?>
                                    <span class="breadcrumb-separator">›</span>
                                <?php endif; ?>
                                <span class="breadcrumb-item <?php echo $index === count($breadcrumbs) - 1 ? 'active' : ''; ?>">
                                    <?php echo $crumb; ?>
                                </span>
                            <?php endforeach; ?>
                        </nav>

                        <h1 class="header-title">
                            <?php echo end($breadcrumbs); ?>
                        </h1>
                    </div>
                    <div class="header-actions">
                        <span class="welcome-text">Welcome back!</span>
                    </div>
                </div>


                <?php if ($current_module === 'settings' || $current_module === 'users'): ?>
                    <nav class="header-submenu">
                        <a href="index.php?module=settings&action=general" class="header-submenu-item <?php echo ($current_module === 'settings' && $action === 'general') ? 'active' : ''; ?>">
                            General
                        </a>
                        <a href="index.php?module=users&action=list" class="header-submenu-item <?php echo ($current_module === 'users') ? 'active' : ''; ?>">
                            Users
                        </a>
                        <a href="index.php?module=settings&action=security" class="header-submenu-item <?php echo ($current_module === 'settings' && $action === 'security') ? 'active' : ''; ?>">
                            Security
                        </a>
                        <a href="index.php?module=settings&action=notifications" class="header-submenu-item <?php echo ($current_module === 'settings' && $action === 'notifications') ? 'active' : ''; ?>">
                            Notifications
                        </a>
                    </nav>
                <?php endif; ?>

                <?php if ($current_module === 'contacts'): ?>
                    <nav class="header-submenu">
                        <a href="index.php?module=contacts&action=clients" class="header-submenu-item <?php echo ($action === 'clients' || $action === 'new_client' || $action === 'edit_client') ? 'active' : ''; ?>">
                            Clients
                        </a>
                        <a href="index.php?module=contacts&action=providers" class="header-submenu-item <?php echo ($action === 'providers' || $action === 'new_provider' || $action === 'edit_provider') ? 'active' : ''; ?>">
                            Providers
                        </a>
                    </nav>
                <?php endif; ?>
            </header>

            <div class="content-body">
                <main>

<script>
function toggleAppMenu() {
    const overlay = document.getElementById('app-menu-overlay');
    const menu = document.getElementById('app-menu');

    overlay.classList.add('show');
    menu.classList.add('show');

    // Prevent body scroll when menu is open
    document.body.style.overflow = 'hidden';
}

function closeAppMenu() {
    const overlay = document.getElementById('app-menu-overlay');
    const menu = document.getElementById('app-menu');

    overlay.classList.remove('show');
    menu.classList.remove('show');

    // Restore body scroll
    document.body.style.overflow = '';
}

// Close menu on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeAppMenu();
    }
});
</script>

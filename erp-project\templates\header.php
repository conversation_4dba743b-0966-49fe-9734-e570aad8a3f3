<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ERP System</title>
    <link rel="stylesheet" href="assets/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Dark Mode Toggle -->
    <button class="theme-toggle" onclick="toggleTheme()" title="Toggle dark mode">
        <span id="theme-icon">🌙</span>
    </button>



    <?php $current_module = $_GET['module'] ?? 'dashboard'; ?>

    <!-- App Menu Button -->
    <button class="app-menu-button" onclick="toggleAppMenu()" title="Open app menu">
        <span class="app-menu-icon">⚏</span>
    </button>

    <!-- Floating App Menu -->
    <div class="app-menu-overlay" id="app-menu-overlay" onclick="closeAppMenu()"></div>
    <div class="app-menu" id="app-menu">
        <div class="app-menu-header">
            <h3>Applications</h3>
            <button class="app-menu-close" onclick="closeAppMenu()">×</button>
        </div>
        <div class="app-menu-grid">
            <a href="index.php?module=dashboard" class="app-menu-item <?php echo ($current_module === 'dashboard') ? 'active' : ''; ?>">
                <div class="app-icon dashboard">🏠</div>
                <span class="app-name">Dashboard</span>
            </a>
            <a href="index.php?module=users&action=list" class="app-menu-item <?php echo ($current_module === 'users') ? 'active' : ''; ?>">
                <div class="app-icon users">👥</div>
                <span class="app-name">Users</span>
            </a>
            <a href="index.php?module=clients&action=list" class="app-menu-item <?php echo ($current_module === 'clients') ? 'active' : ''; ?>">
                <div class="app-icon clients">👤</div>
                <span class="app-name">Clients</span>
            </a>
            <a href="index.php?module=products&action=list" class="app-menu-item <?php echo ($current_module === 'products') ? 'active' : ''; ?>">
                <div class="app-icon products">📦</div>
                <span class="app-name">Products</span>
            </a>
            <a href="index.php?module=sales&action=list" class="app-menu-item <?php echo ($current_module === 'sales') ? 'active' : ''; ?>">
                <div class="app-icon sales">📈</div>
                <span class="app-name">Sales</span>
            </a>
            <a href="index.php?module=auth&action=logout" class="app-menu-item">
                <div class="app-icon logout">🚪</div>
                <span class="app-name">Logout</span>
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content" id="main-content">
            <header class="content-header">
                <div>
                    <h1 style="margin: 0; font-size: 1.25rem; font-weight: 600;">
                        <?php
                        $titles = [
                            'dashboard' => 'Dashboard',
                            'users' => 'User Management',
                            'clients' => 'Client Management',
                            'products' => 'Product Management',
                            'sales' => 'Sales Management'
                        ];
                        echo $titles[$current_module] ?? 'ERP System';
                        ?>
                    </h1>
                </div>
                <div class="flex items-center gap-4">
                    <span class="text-sm text-muted">Welcome back!</span>
                </div>
            </header>

            <div class="content-body">
                <main>

<script>
function toggleAppMenu() {
    const overlay = document.getElementById('app-menu-overlay');
    const menu = document.getElementById('app-menu');

    overlay.classList.add('show');
    menu.classList.add('show');

    // Prevent body scroll when menu is open
    document.body.style.overflow = 'hidden';
}

function closeAppMenu() {
    const overlay = document.getElementById('app-menu-overlay');
    const menu = document.getElementById('app-menu');

    overlay.classList.remove('show');
    menu.classList.remove('show');

    // Restore body scroll
    document.body.style.overflow = '';
}

// Close menu on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeAppMenu();
    }
});
</script>

<div class="table-container">
    <div class="table-header">
        <h2 class="table-title">Product Inventory</h2>
        <div class="table-actions">
            <div class="table-search">
                <input type="text" placeholder="Search products..." id="productSearch" onkeyup="searchTable('productsTable', 'productSearch')">
            </div>
            <a href="index.php?module=products&action=new" class="button">
                <span>✨</span>
                <span>New Product</span>
            </a>
        </div>
    </div>

    <table class="data-table" id="productsTable">
        <thead>
            <tr>
                <th>ID</th>
                <th>Product</th>
                <th>SKU</th>
                <th>Stock</th>
                <th>Price</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php if (!empty($products)): ?>
                <?php foreach ($products as $product): ?>
                    <tr>
                        <td>
                            <span style="font-weight: 600; color: hsl(var(--muted-foreground));">
                                #<?php echo $product['id']; ?>
                            </span>
                        </td>
                        <td>
                            <div style="display: flex; align-items: center; gap: var(--spacing-md);">
                                <div style="width: 32px; height: 32px; border-radius: var(--radius); background: var(--gradient-primary); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 0.875rem;">
                                    📦
                                </div>
                                <span style="font-weight: 500;">
                                    <?php echo htmlspecialchars($product['name']); ?>
                                </span>
                            </div>
                        </td>
                        <td>
                            <span style="font-family: monospace; background: hsl(var(--muted)); padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius); font-size: 0.875rem;">
                                <?php echo htmlspecialchars($product['sku']); ?>
                            </span>
                        </td>
                        <td>
                            <?php
                            $quantity = (int)$product['quantity'];
                            $stockClass = $quantity <= 10 ? 'destructive' : ($quantity <= 50 ? 'warning' : 'success');
                            $stockIcon = $quantity <= 10 ? '⚠️' : ($quantity <= 50 ? '📊' : '✅');
                            ?>
                            <div style="display: flex; align-items: center; gap: var(--spacing-xs);">
                                <span><?php echo $stockIcon; ?></span>
                                <span style="font-weight: 500; color: hsl(var(--<?php echo $stockClass; ?>));">
                                    <?php echo $quantity; ?>
                                </span>
                                <span style="color: hsl(var(--muted-foreground)); font-size: 0.875rem;">units</span>
                            </div>
                        </td>
                        <td>
                            <span style="font-weight: 600; color: hsl(var(--foreground));">
                                $<?php echo number_format($product['price'], 2); ?>
                            </span>
                        </td>
                        <td>
                            <div class="table-actions-cell">
                                <a href="index.php?module=products&action=edit&id=<?php echo $product['id']; ?>" class="action-button edit" title="Edit product">
                                    ✏️ Edit
                                </a>
                                <a href="index.php?module=products&action=delete&id=<?php echo $product['id']; ?>" class="action-button delete" title="Delete product" onclick="return confirm('Are you sure you want to delete this product?')">
                                    🗑️ Delete
                                </a>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php else: ?>
                <tr>
                    <td colspan="6" style="text-align: center; padding: var(--spacing-2xl); color: hsl(var(--muted-foreground));">
                        <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">📦</div>
                        <p style="font-size: 1.125rem; margin-bottom: var(--spacing-sm);">No products found</p>
                        <p style="font-size: 0.875rem;">Start by adding your first product to inventory</p>
                    </td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>

    <?php if (!empty($products)): ?>
        <div class="table-pagination">
            <div class="pagination-info">
                Showing <?php echo count($products); ?> product(s)
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
function searchTable(tableId, searchId) {
    const input = document.getElementById(searchId);
    const filter = input.value.toLowerCase();
    const table = document.getElementById(tableId);
    const rows = table.getElementsByTagName('tr');

    for (let i = 1; i < rows.length; i++) {
        const cells = rows[i].getElementsByTagName('td');
        let found = false;

        for (let j = 0; j < cells.length - 1; j++) { // Exclude actions column
            if (cells[j].textContent.toLowerCase().includes(filter)) {
                found = true;
                break;
            }
        }

        rows[i].style.display = found ? '' : 'none';
    }
}
</script>

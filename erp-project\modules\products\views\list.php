<!-- Success/Error Messages -->
<?php if (isset($_GET['success'])): ?>
    <div class="alert alert-success">
        <i data-lucide="check-circle"></i>
        <span>Product added successfully!</span>
    </div>
<?php endif; ?>

<?php if (isset($_GET['updated'])): ?>
    <div class="alert alert-success">
        <i data-lucide="check-circle"></i>
        <span>
            <?php
            $count = intval($_GET['updated']);
            echo $count > 1 ? "$count products updated successfully!" : "Product updated successfully!";
            ?>
        </span>
    </div>
<?php endif; ?>

<?php if (isset($_GET['deleted'])): ?>
    <div class="alert alert-success">
        <i data-lucide="check-circle"></i>
        <span>
            <?php
            $count = intval($_GET['deleted']);
            echo $count > 1 ? "$count products deleted successfully!" : "Product deleted successfully!";
            ?>
        </span>
    </div>
<?php endif; ?>

<?php if (isset($_GET['error'])): ?>
    <div class="alert alert-error">
        <i data-lucide="alert-circle"></i>
        <span>An error occurred. Please try again.</span>
    </div>
<?php endif; ?>

<div class="table-container">
    <div class="table-header">
        <div class="table-title">
            <h2>Product Inventory</h2>
            <p>Manage your product catalog and inventory</p>
        </div>
        <div class="table-actions">
            <div id="bulkActionsProducts" style="display: none; margin-right: 8px; gap: 6px;">
                <button class="bulk-action-btn bulk-delete" onclick="bulkDeleteProducts()" title="Delete selected products">
                    <i data-lucide="trash-2"></i>
                    <span>Delete</span>
                </button>
                <button class="bulk-action-btn bulk-activate" onclick="bulkActivateProducts()" title="Activate selected products">
                    <i data-lucide="check-circle"></i>
                    <span>Activate</span>
                </button>
                <button class="bulk-action-btn bulk-deactivate" onclick="bulkDeactivateProducts()" title="Deactivate selected products">
                    <i data-lucide="pause-circle"></i>
                    <span>Deactivate</span>
                </button>
            </div>
            <div class="search-box">
                <i data-lucide="search"></i>
                <input type="text" id="productSearch" placeholder="Search products..." class="search-input">
            </div>
            <div class="filter-dropdown">
                <button class="button button-outline" id="filterBtnProducts" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px); border: 1px solid hsl(var(--border) / 0.3); box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    <i data-lucide="sliders-horizontal"></i>
                    <span>Filter</span>
                </button>
                <div class="filter-dropdown-content" id="filterDropdownProducts">
                    <div class="filter-option active" data-filter="all">All Products</div>
                    <div class="filter-option" data-filter="active">Active Only</div>
                    <div class="filter-option" data-filter="inactive">Inactive Only</div>
                    <div class="filter-option" data-filter="low-stock">Low Stock</div>
                    <div class="filter-option" data-filter="out-of-stock">Out of Stock</div>
                </div>
            </div>
            <a href="index.php?module=products&action=new" class="button" style="background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary) / 0.8) 100%); color: white; box-shadow: 0 2px 8px hsl(var(--primary) / 0.3);">
                <i data-lucide="package-plus"></i>
                <span>Add Product</span>
            </a>
        </div>
    </div>

    <div class="table-content">
        <?php if (empty($products)): ?>
            <div style="text-align: center; padding: var(--spacing-2xl); color: hsl(var(--muted-foreground));">
                <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">
                    <i data-lucide="package" style="width: 48px; height: 48px; stroke-width: 1;"></i>
                </div>
                <h3 style="margin-bottom: var(--spacing-sm);">No products found</h3>
                <p style="margin-bottom: var(--spacing-lg);">Start by adding your first product to inventory</p>
                <a href="index.php?module=products&action=new" class="button">
                    <i data-lucide="plus"></i>
                    <span>Add Product</span>
                </a>
            </div>
        <?php else: ?>
            <table class="table" id="productsTable">
                <thead>
                    <tr>
                        <th style="width: 3%;">
                            <input type="checkbox" id="selectAllProducts" style="margin: 0;">
                        </th>
                        <th style="width: 5%;">ID</th>
                        <th style="width: 35%; cursor: pointer; user-select: none;" onclick="sortProductsTable('name')" onmouseover="this.style.background='hsl(var(--muted) / 0.1)'" onmouseout="this.style.background=''">
                            Product <i data-lucide="arrow-up-down" style="width: 12px; height: 12px; display: inline; opacity: 0.6; transition: opacity 0.2s;"></i>
                        </th>
                        <th style="width: 15%;">SKU</th>
                        <th style="width: 12%; cursor: pointer; user-select: none;" onclick="sortProductsTable('price')" onmouseover="this.style.background='hsl(var(--muted) / 0.1)'" onmouseout="this.style.background=''">
                            Price <i data-lucide="arrow-up-down" style="width: 12px; height: 12px; display: inline; opacity: 0.6; transition: opacity 0.2s;"></i>
                        </th>
                        <th style="width: 10%;">Stock</th>
                        <th style="width: 20%;">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($products as $product): ?>
                        <tr class="product-row"
                            data-name="<?php echo strtolower(htmlspecialchars($product['name'])); ?>"
                            data-sku="<?php echo strtolower(htmlspecialchars($product['sku'] ?? '')); ?>"
                            data-price="<?php echo $product['price'] ?? 0; ?>"
                            data-stock="<?php echo $product['quantity'] ?? 0; ?>"
                            data-id="<?php echo $product['id']; ?>">
                            <td>
                                <input type="checkbox" class="row-checkbox-product" value="<?php echo $product['id']; ?>" style="margin: 0;">
                            </td>
                            <td>
                                <span style="font-weight: 600; color: hsl(var(--muted-foreground)); font-size: 0.8rem;">
                                    #<?php echo $product['id']; ?>
                                </span>
                            </td>
                            <td>
                                <div class="user-info">
                                    <div class="user-avatar" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
                                        <i data-lucide="package" style="width: 14px; height: 14px;"></i>
                                    </div>
                                    <div class="user-details">
                                        <div class="user-name"><?php echo htmlspecialchars($product['name']); ?></div>
                                        <div class="user-role">Product</div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span style="font-family: monospace; background: hsl(var(--muted) / 0.3); padding: 4px 6px; border-radius: var(--radius); font-size: 0.75rem; color: hsl(var(--muted-foreground));">
                                    <?php echo htmlspecialchars($product['sku'] ?? 'N/A'); ?>
                                </span>
                            </td>
                            <td>
                                <span style="font-weight: 600; color: hsl(var(--foreground)); font-size: 0.85rem;">
                                    $<?php echo number_format($product['price'] ?? 0, 2); ?>
                                </span>
                            </td>
                            <td>
                                <?php
                                $quantity = (int)($product['quantity'] ?? 0);
                                $stockColor = $quantity <= 10 ? 'var(--destructive)' : ($quantity <= 50 ? 'var(--warning)' : 'var(--success)');
                                ?>
                                <span class="status-badge" style="background: hsl(<?php echo $stockColor; ?> / 0.1); color: hsl(<?php echo $stockColor; ?>); border: 1px solid hsl(<?php echo $stockColor; ?> / 0.2);">
                                    <?php echo $quantity; ?> units
                                </span>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <a href="index.php?module=products&action=edit&id=<?php echo $product['id']; ?>"
                                       class="button button-sm button-edit" title="Edit Product">
                                        <i data-lucide="edit-3"></i>
                                    </a>
                                    <button onclick="viewProduct(<?php echo $product['id']; ?>)"
                                            class="button button-sm button-view"
                                            title="View Product Details">
                                        <i data-lucide="eye"></i>
                                    </button>
                                    <button onclick="deleteProduct(<?php echo $product['id']; ?>, '<?php echo htmlspecialchars($product['name']); ?>')"
                                            class="button button-sm button-destructive"
                                            title="Delete Product">
                                        <i data-lucide="trash-2"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>

    <?php if (!empty($products)): ?>
        <div class="table-footer" style="padding: 8px 12px; border-top: 1px solid hsl(var(--border) / 0.15); background: hsl(var(--muted) / 0.05); display: flex; align-items: center; justify-content: space-between;">
            <div style="font-size: 0.8rem; color: hsl(var(--muted-foreground));">
                <span id="productCount">Showing <?php echo count($products); ?> of <?php echo count($products); ?> products</span>
            </div>
            <div style="display: flex; align-items: center; gap: 4px;">
                <button class="button button-outline button-sm" disabled>
                    <i data-lucide="chevron-left"></i>
                    <span>Previous</span>
                </button>
                <button class="button button-outline button-sm" disabled>
                    <span>Next</span>
                    <i data-lucide="chevron-right"></i>
                </button>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Floating Action Button -->
<button class="fab" onclick="window.location.href='index.php?module=products&action=new'" title="Add New Product">
    <i data-lucide="plus"></i>
</button>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize enhanced table functionality
    initializeEnhancedTable('productsTable', {
        searchInputId: 'productSearch',
        filterButtonId: 'filterBtnProducts',
        filterDropdownId: 'filterDropdownProducts',
        selectAllId: 'selectAllProducts',
        bulkActionsId: 'bulkActionsProducts',
        countElementId: 'productCount',
        rowSelector: '.product-row',
        checkboxSelector: '.row-checkbox-product'
    });
});

function sortProductsTable(column) {
    sortTable('productsTable', column, column);
}

function getSelectedProductIds() {
    return Array.from(document.querySelectorAll('.row-checkbox-product:checked')).map(cb => cb.value);
}

function bulkDeleteProducts() {
    handleBulkAction('bulk_delete', 'products', '.row-checkbox-product',
        'Are you sure you want to delete {count} selected product(s)?\n\nThis action cannot be undone.');
}

function bulkActivateProducts() {
    const selectedIds = getSelectedProductIds();
    if (selectedIds.length === 0) return;

    if (confirm(`Activate ${selectedIds.length} selected product(s)?`)) {
        bulkUpdateProductStatus(selectedIds, 'Active');
    }
}

function bulkDeactivateProducts() {
    const selectedIds = getSelectedProductIds();
    if (selectedIds.length === 0) return;

    if (confirm(`Deactivate ${selectedIds.length} selected product(s)?`)) {
        bulkUpdateProductStatus(selectedIds, 'Inactive');
    }
}

function bulkUpdateProductStatus(ids, status) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = 'index.php?module=products&action=bulk_update';

    ids.forEach(id => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'product_ids[]';
        input.value = id;
        form.appendChild(input);
    });

    const statusInput = document.createElement('input');
    statusInput.type = 'hidden';
    statusInput.name = 'status';
    statusInput.value = status;
    form.appendChild(statusInput);

    document.body.appendChild(form);
    form.submit();
}

function viewProduct(id) {
    window.location.href = `index.php?module=products&action=view&id=${id}`;
}

function deleteProduct(id, name) {
    confirmDelete(name, `index.php?module=products&action=delete&id=${id}`);
}
</script>

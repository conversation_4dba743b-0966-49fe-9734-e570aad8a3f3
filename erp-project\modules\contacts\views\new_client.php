<!-- Error Messages -->
<?php if (isset($_GET['error'])): ?>
    <div class="alert alert-error">
        <i data-lucide="alert-circle"></i>
        <span>Error adding client. Please try again.</span>
    </div>
<?php endif; ?>

<!-- Page Header -->
<div class="table-container">
    <div class="table-header">
        <div class="table-title">
            <h2>Add New Client</h2>
            <p>Create a new client record</p>
        </div>
        <div class="table-actions">
            <a href="index.php?module=contacts&action=clients" class="button button-outline">
                <i data-lucide="arrow-left"></i>
                <span>Back</span>
            </a>
        </div>
    </div>
</div>

<!-- Client Form -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Client Information</h3>
        <p class="card-description">Enter client details</p>
    </div>
    
    <form method="POST" class="card-content">
        <div class="form-grid">
            <div class="form-group">
                <label for="name" class="form-label">Company Name *</label>
                <input type="text" id="name" name="name" class="form-input" required 
                       placeholder="Enter company name">
            </div>
            
            <div class="form-group">
                <label for="contact_person" class="form-label">Contact Person</label>
                <input type="text" id="contact_person" name="contact_person" class="form-input" 
                       placeholder="Enter contact person name">
            </div>
            
            <div class="form-group">
                <label for="email" class="form-label">Email Address *</label>
                <input type="email" id="email" name="email" class="form-input" required 
                       placeholder="Enter email address">
            </div>
            
            <div class="form-group">
                <label for="phone" class="form-label">Phone Number</label>
                <input type="tel" id="phone" name="phone" class="form-input" 
                       placeholder="Enter phone number">
            </div>
            
            <div class="form-group">
                <label for="website" class="form-label">Website</label>
                <input type="url" id="website" name="website" class="form-input" 
                       placeholder="https://example.com">
            </div>
            
            <div class="form-group">
                <label for="industry" class="form-label">Industry</label>
                <select id="industry" name="industry" class="form-select">
                    <option value="">Select industry</option>
                    <option value="technology">Technology</option>
                    <option value="healthcare">Healthcare</option>
                    <option value="finance">Finance</option>
                    <option value="retail">Retail</option>
                    <option value="manufacturing">Manufacturing</option>
                    <option value="other">Other</option>
                </select>
            </div>
            
            <div class="form-group form-group-full">
                <label for="address" class="form-label">Address</label>
                <textarea id="address" name="address" class="form-textarea" rows="3" 
                          placeholder="Enter full address"></textarea>
            </div>
            
            <div class="form-group">
                <label for="city" class="form-label">City</label>
                <input type="text" id="city" name="city" class="form-input" 
                       placeholder="Enter city">
            </div>
            
            <div class="form-group">
                <label for="state" class="form-label">State/Province</label>
                <input type="text" id="state" name="state" class="form-input" 
                       placeholder="Enter state or province">
            </div>
            
            <div class="form-group">
                <label for="postal_code" class="form-label">Postal Code</label>
                <input type="text" id="postal_code" name="postal_code" class="form-input" 
                       placeholder="Enter postal code">
            </div>
            
            <div class="form-group">
                <label for="country" class="form-label">Country</label>
                <select id="country" name="country" class="form-select">
                    <option value="">Select country</option>
                    <option value="US">United States</option>
                    <option value="CA">Canada</option>
                    <option value="UK">United Kingdom</option>
                    <option value="AU">Australia</option>
                    <option value="DE">Germany</option>
                    <option value="FR">France</option>
                    <option value="other">Other</option>
                </select>
            </div>
            
            <div class="form-group form-group-full">
                <label for="notes" class="form-label">Notes</label>
                <textarea id="notes" name="notes" class="form-textarea" rows="4" 
                          placeholder="Additional notes about this client"></textarea>
            </div>
        </div>
        
        <div class="card-footer">
            <div class="form-actions">
                <a href="index.php?module=contacts&action=clients" class="button button-outline" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px); border: 1px solid hsl(var(--border) / 0.3);">
                    <i data-lucide="arrow-left"></i>
                    <span>Cancel</span>
                </a>
                <button type="submit" class="button" style="background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary) / 0.8) 100%); color: white; box-shadow: 0 2px 8px hsl(var(--primary) / 0.3); border: none;">
                    <i data-lucide="user-plus"></i>
                    <span>Create Client</span>
                </button>
            </div>
        </div>
    </form>
</div>

<!-- Clients Management -->
<div class="content-header">
    <div class="header-top">
        <div class="header-left">
            <h1 class="page-title">Contacts</h1>
            <p class="page-description">Manage your business contacts</p>
        </div>
        <div class="header-actions">
            <a href="index.php?module=contacts&action=new_client" class="button">
                <i data-lucide="user-plus"></i>
                <span>Add Client</span>
            </a>
        </div>
    </div>
    
    <!-- Header Sub-menu (Odoo-style) -->
    <div class="header-submenu">
        <a href="index.php?module=contacts&action=clients" class="header-submenu-item active">
            <i data-lucide="users"></i>
            <span>Clients</span>
        </a>
        <a href="index.php?module=contacts&action=providers" class="header-submenu-item">
            <i data-lucide="truck"></i>
            <span>Providers</span>
        </a>
    </div>
</div>

<!-- Success/Error Messages -->
<?php if (isset($_GET['success'])): ?>
    <div class="alert alert-success">
        <i data-lucide="check-circle"></i>
        <span>Client added successfully!</span>
    </div>
<?php endif; ?>

<?php if (isset($_GET['updated'])): ?>
    <div class="alert alert-success">
        <i data-lucide="check-circle"></i>
        <span>Client updated successfully!</span>
    </div>
<?php endif; ?>

<?php if (isset($_GET['deleted'])): ?>
    <div class="alert alert-success">
        <i data-lucide="check-circle"></i>
        <span>Client deleted successfully!</span>
    </div>
<?php endif; ?>

<!-- Clients Table -->
<div class="card">
    <div class="card-header">
        <div class="table-header">
            <div class="table-title">
                <h3>Clients</h3>
                <p>Manage your client relationships</p>
            </div>
            <div class="table-actions">
                <div class="search-box">
                    <i data-lucide="search"></i>
                    <input type="text" placeholder="Search clients..." class="search-input">
                </div>
                <button class="button button-outline">
                    <i data-lucide="filter"></i>
                    <span>Filter</span>
                </button>
            </div>
        </div>
    </div>
    
    <div class="card-content">
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Phone</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($clients as $client): ?>
                        <tr>
                            <td>
                                <div class="user-info">
                                    <div class="user-avatar">
                                        <?php echo substr($client['name'], 0, 1); ?>
                                    </div>
                                    <div class="user-details">
                                        <div class="user-name"><?php echo htmlspecialchars($client['name']); ?></div>
                                        <div class="user-role">Client</div>
                                    </div>
                                </div>
                            </td>
                            <td><?php echo htmlspecialchars($client['email']); ?></td>
                            <td><?php echo htmlspecialchars($client['phone']); ?></td>
                            <td>
                                <span class="status-badge <?php echo strtolower($client['status']); ?>">
                                    <?php echo $client['status']; ?>
                                </span>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <a href="index.php?module=contacts&action=edit_client&id=<?php echo $client['id']; ?>" 
                                       class="button button-sm button-outline" title="Edit">
                                        <i data-lucide="edit"></i>
                                    </a>
                                    <a href="index.php?module=contacts&action=delete_client&id=<?php echo $client['id']; ?>" 
                                       class="button button-sm button-outline button-destructive" 
                                       title="Delete"
                                       onclick="return confirm('Are you sure you want to delete this client?')">
                                        <i data-lucide="trash-2"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
    
    <div class="card-footer">
        <div class="table-info">
            <span>Showing <?php echo count($clients); ?> of <?php echo count($clients); ?> clients</span>
        </div>
        <div class="pagination">
            <button class="button button-outline button-sm" disabled>
                <i data-lucide="chevron-left"></i>
                <span>Previous</span>
            </button>
            <button class="button button-outline button-sm" disabled>
                <span>Next</span>
                <i data-lucide="chevron-right"></i>
            </button>
        </div>
    </div>
</div>

<!-- Success/Error Messages -->
<?php if (isset($_GET['success'])): ?>
    <div class="alert alert-success">
        <i data-lucide="check-circle"></i>
        <span>Client added successfully!</span>
    </div>
<?php endif; ?>

<?php if (isset($_GET['updated'])): ?>
    <div class="alert alert-success">
        <i data-lucide="check-circle"></i>
        <span>
            <?php
            $count = intval($_GET['updated']);
            echo $count > 1 ? "$count clients updated successfully!" : "Client updated successfully!";
            ?>
        </span>
    </div>
<?php endif; ?>

<?php if (isset($_GET['deleted'])): ?>
    <div class="alert alert-success">
        <i data-lucide="check-circle"></i>
        <span>
            <?php
            $count = intval($_GET['deleted']);
            echo $count > 1 ? "$count clients deleted successfully!" : "Client deleted successfully!";
            ?>
        </span>
    </div>
<?php endif; ?>

<?php if (isset($_GET['error'])): ?>
    <div class="alert alert-error">
        <i data-lucide="alert-circle"></i>
        <span>An error occurred. Please try again.</span>
    </div>
<?php endif; ?>

<!-- Clients Table -->
<div class="table-container">
    <div class="table-header">
        <div class="table-title">
            <h2>Clients</h2>
            <p>Manage your client relationships</p>
        </div>
        <div class="table-actions">
            <div id="bulkActions" style="display: none; margin-right: 8px;">
                <button class="button button-outline button-sm" onclick="bulkDelete()">
                    <i data-lucide="trash-2"></i>
                    <span>Delete Selected</span>
                </button>
                <button class="button button-outline button-sm" onclick="bulkActivate()">
                    <i data-lucide="check"></i>
                    <span>Activate</span>
                </button>
                <button class="button button-outline button-sm" onclick="bulkDeactivate()">
                    <i data-lucide="x"></i>
                    <span>Deactivate</span>
                </button>
            </div>
            <div class="search-box">
                <i data-lucide="search"></i>
                <input type="text" id="clientSearch" placeholder="Search clients..." class="search-input">
            </div>
            <div class="filter-dropdown">
                <button class="button button-outline" id="filterBtn">
                    <i data-lucide="filter"></i>
                    <span>Filter</span>
                </button>
                <div class="filter-dropdown-content" id="filterDropdown">
                    <div class="filter-option active" data-filter="all">All Clients</div>
                    <div class="filter-option" data-filter="active">Active Only</div>
                    <div class="filter-option" data-filter="inactive">Inactive Only</div>
                    <div class="filter-option" data-filter="technology">Technology</div>
                    <div class="filter-option" data-filter="healthcare">Healthcare</div>
                    <div class="filter-option" data-filter="finance">Finance</div>
                    <div class="filter-option" data-filter="retail">Retail</div>
                </div>
            </div>
            <a href="index.php?module=contacts&action=new_client" class="button">
                <i data-lucide="plus"></i>
                <span>Add</span>
            </a>
        </div>
    </div>

    <div class="table-content">
        <?php if (empty($clients)): ?>
            <div style="text-align: center; padding: var(--spacing-2xl); color: hsl(var(--muted-foreground));">
                <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">
                    <i data-lucide="users" style="width: 48px; height: 48px; stroke-width: 1;"></i>
                </div>
                <h3 style="margin-bottom: var(--spacing-sm);">No clients found</h3>
                <p style="margin-bottom: var(--spacing-lg);">Start by adding your first client</p>
                <a href="index.php?module=contacts&action=new_client" class="button">
                    <i data-lucide="plus"></i>
                    <span>Add Client</span>
                </a>
            </div>
        <?php else: ?>
            <table class="table" id="clientsTable">
                <thead>
                    <tr>
                        <th style="width: 3%;">
                            <input type="checkbox" id="selectAll" style="margin: 0;">
                        </th>
                        <th style="width: 32%; cursor: pointer;" onclick="sortTable('name')">
                            Client <i data-lucide="chevrons-up-down" style="width: 12px; height: 12px; display: inline;"></i>
                        </th>
                        <th style="width: 25%;">Contact</th>
                        <th style="width: 15%; cursor: pointer;" onclick="sortTable('industry')">
                            Industry <i data-lucide="chevrons-up-down" style="width: 12px; height: 12px; display: inline;"></i>
                        </th>
                        <th style="width: 10%; cursor: pointer;" onclick="sortTable('status')">
                            Status <i data-lucide="chevrons-up-down" style="width: 12px; height: 12px; display: inline;"></i>
                        </th>
                        <th style="width: 15%;">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($clients as $client): ?>
                        <tr class="client-row"
                            data-name="<?php echo strtolower(htmlspecialchars($client['name'])); ?>"
                            data-email="<?php echo strtolower(htmlspecialchars($client['email'])); ?>"
                            data-industry="<?php echo strtolower(htmlspecialchars($client['industry'] ?? '')); ?>"
                            data-status="<?php echo strtolower($client['status']); ?>"
                            data-id="<?php echo $client['id']; ?>">
                            <td>
                                <input type="checkbox" class="row-checkbox" value="<?php echo $client['id']; ?>" style="margin: 0;">
                            </td>
                            <td>
                                <div class="user-info">
                                    <div class="user-avatar">
                                        <?php echo strtoupper(substr($client['name'], 0, 1)); ?>
                                    </div>
                                    <div class="user-details">
                                        <div class="user-name"><?php echo htmlspecialchars($client['name']); ?></div>
                                        <div class="user-role"><?php echo htmlspecialchars($client['contact_person'] ?? 'No contact'); ?></div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div style="display: flex; flex-direction: column; gap: 1px;">
                                    <span style="font-weight: 500; font-size: 0.8rem;"><?php echo htmlspecialchars($client['email']); ?></span>
                                    <?php if (!empty($client['phone'])): ?>
                                        <span style="font-size: 0.7rem; color: hsl(var(--muted-foreground));"><?php echo htmlspecialchars($client['phone']); ?></span>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <?php if (!empty($client['industry'])): ?>
                                    <span class="status-badge" style="background: hsl(var(--primary) / 0.1); color: hsl(var(--primary)); border: 1px solid hsl(var(--primary) / 0.2);">
                                        <?php echo ucfirst(htmlspecialchars($client['industry'])); ?>
                                    </span>
                                <?php else: ?>
                                    <span style="color: hsl(var(--muted-foreground)); font-size: 0.8rem;">—</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="status-badge <?php echo strtolower($client['status']); ?>">
                                    <?php echo $client['status']; ?>
                                </span>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <a href="index.php?module=contacts&action=edit_client&id=<?php echo $client['id']; ?>"
                                       class="button button-sm button-outline" title="Edit Client">
                                        <i data-lucide="edit"></i>
                                    </a>
                                    <button onclick="deleteClient(<?php echo $client['id']; ?>, '<?php echo htmlspecialchars($client['name']); ?>')"
                                            class="button button-sm button-outline button-destructive"
                                            title="Delete Client">
                                        <i data-lucide="trash-2"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>

    <?php if (!empty($clients)): ?>
        <div class="table-footer" style="padding: 8px 12px; border-top: 1px solid hsl(var(--border) / 0.15); background: hsl(var(--muted) / 0.05); display: flex; align-items: center; justify-content: space-between;">
            <div style="font-size: 0.8rem; color: hsl(var(--muted-foreground));">
                <span id="clientCount">Showing <?php echo count($clients); ?> of <?php echo count($clients); ?> clients</span>
            </div>
            <div style="display: flex; align-items: center; gap: 4px;">
                <button class="button button-outline button-sm" disabled>
                    <i data-lucide="chevron-left"></i>
                    <span>Previous</span>
                </button>
                <button class="button button-outline button-sm" disabled>
                    <span>Next</span>
                    <i data-lucide="chevron-right"></i>
                </button>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('clientSearch');
    const filterBtn = document.getElementById('filterBtn');
    const filterDropdown = document.getElementById('filterDropdown');
    const filterOptions = document.querySelectorAll('.filter-option');
    const clientRows = document.querySelectorAll('.client-row');
    const clientCount = document.getElementById('clientCount');
    const selectAllCheckbox = document.getElementById('selectAll');
    const rowCheckboxes = document.querySelectorAll('.row-checkbox');
    const bulkActions = document.getElementById('bulkActions');
    const totalClients = clientRows.length;

    let currentFilter = 'all';
    let sortDirection = {};

    // Search functionality
    searchInput.addEventListener('input', function() {
        filterClients();
    });

    // Filter dropdown toggle
    filterBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        filterDropdown.classList.toggle('show');
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', function() {
        filterDropdown.classList.remove('show');
    });

    // Filter options
    filterOptions.forEach(option => {
        option.addEventListener('click', function() {
            filterOptions.forEach(opt => opt.classList.remove('active'));
            this.classList.add('active');
            currentFilter = this.dataset.filter;
            filterBtn.querySelector('span').textContent = this.textContent;
            filterDropdown.classList.remove('show');
            filterClients();
        });
    });

    // Select all functionality
    selectAllCheckbox.addEventListener('change', function() {
        const isChecked = this.checked;
        rowCheckboxes.forEach(checkbox => {
            if (checkbox.closest('.client-row').style.display !== 'none') {
                checkbox.checked = isChecked;
            }
        });
        updateBulkActions();
    });

    // Individual checkbox functionality
    rowCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateBulkActions();
            updateSelectAll();
        });
    });

    function updateBulkActions() {
        const checkedBoxes = Array.from(rowCheckboxes).filter(cb =>
            cb.checked && cb.closest('.client-row').style.display !== 'none'
        );
        bulkActions.style.display = checkedBoxes.length > 0 ? 'flex' : 'none';
    }

    function updateSelectAll() {
        const visibleCheckboxes = Array.from(rowCheckboxes).filter(cb =>
            cb.closest('.client-row').style.display !== 'none'
        );
        const checkedVisible = visibleCheckboxes.filter(cb => cb.checked);

        selectAllCheckbox.checked = visibleCheckboxes.length > 0 &&
                                   checkedVisible.length === visibleCheckboxes.length;
        selectAllCheckbox.indeterminate = checkedVisible.length > 0 &&
                                         checkedVisible.length < visibleCheckboxes.length;
    }

    function filterClients() {
        const searchTerm = searchInput.value.toLowerCase();
        let visibleCount = 0;

        clientRows.forEach(row => {
            const name = row.dataset.name;
            const email = row.dataset.email;
            const industry = row.dataset.industry;
            const status = row.dataset.status;

            const searchMatch = name.includes(searchTerm) ||
                              email.includes(searchTerm) ||
                              industry.includes(searchTerm);

            let filterMatch = true;
            if (currentFilter !== 'all') {
                if (currentFilter === 'active' || currentFilter === 'inactive') {
                    filterMatch = status === currentFilter;
                } else {
                    filterMatch = industry === currentFilter;
                }
            }

            if (searchMatch && filterMatch) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
                row.querySelector('.row-checkbox').checked = false;
            }
        });

        clientCount.textContent = `Showing ${visibleCount} of ${totalClients} clients`;
        updateBulkActions();
        updateSelectAll();
    }
});

function sortTable(column) {
    const table = document.getElementById('clientsTable');
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('.client-row'));

    // Toggle sort direction
    sortDirection[column] = sortDirection[column] === 'asc' ? 'desc' : 'asc';

    rows.sort((a, b) => {
        let aVal, bVal;

        switch(column) {
            case 'name':
                aVal = a.dataset.name;
                bVal = b.dataset.name;
                break;
            case 'industry':
                aVal = a.dataset.industry;
                bVal = b.dataset.industry;
                break;
            case 'status':
                aVal = a.dataset.status;
                bVal = b.dataset.status;
                break;
            default:
                return 0;
        }

        if (sortDirection[column] === 'asc') {
            return aVal.localeCompare(bVal);
        } else {
            return bVal.localeCompare(aVal);
        }
    });

    // Re-append sorted rows
    rows.forEach(row => tbody.appendChild(row));
}

function getSelectedIds() {
    return Array.from(document.querySelectorAll('.row-checkbox:checked')).map(cb => cb.value);
}

function bulkDelete() {
    const selectedIds = getSelectedIds();
    if (selectedIds.length === 0) return;

    if (confirm(`Are you sure you want to delete ${selectedIds.length} selected client(s)?\\n\\nThis action cannot be undone.`)) {
        // Create form to submit multiple IDs
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'index.php?module=contacts&action=bulk_delete_clients';

        selectedIds.forEach(id => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'client_ids[]';
            input.value = id;
            form.appendChild(input);
        });

        document.body.appendChild(form);
        form.submit();
    }
}

function bulkActivate() {
    const selectedIds = getSelectedIds();
    if (selectedIds.length === 0) return;

    if (confirm(`Activate ${selectedIds.length} selected client(s)?`)) {
        bulkUpdateStatus(selectedIds, 'Active');
    }
}

function bulkDeactivate() {
    const selectedIds = getSelectedIds();
    if (selectedIds.length === 0) return;

    if (confirm(`Deactivate ${selectedIds.length} selected client(s)?`)) {
        bulkUpdateStatus(selectedIds, 'Inactive');
    }
}

function bulkUpdateStatus(ids, status) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = 'index.php?module=contacts&action=bulk_update_clients';

    ids.forEach(id => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'client_ids[]';
        input.value = id;
        form.appendChild(input);
    });

    const statusInput = document.createElement('input');
    statusInput.type = 'hidden';
    statusInput.name = 'status';
    statusInput.value = status;
    form.appendChild(statusInput);

    document.body.appendChild(form);
    form.submit();
}

function deleteClient(id, name) {
    if (confirm(`Are you sure you want to delete "${name}"?\\n\\nThis action cannot be undone.`)) {
        window.location.href = `index.php?module=contacts&action=delete_client&id=${id}`;
    }
}
</script>

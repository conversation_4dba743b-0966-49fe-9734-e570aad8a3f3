/* public/assets/style.css */

/* --- Modern Premium Design System --- */
:root {
    /* Light theme - Inspired by Linear, Notion, Vercel */
    --background: 250 250% 99%;
    --foreground: 240 10% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    --primary: 262 83% 58%;
    --primary-foreground: 210 40% 98%;

    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;

    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;

    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 262 83% 58%;

    /* Enhanced color palette */
    --success: 142 71% 45%;
    --success-foreground: 0 0% 98%;
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 98%;
    --info: 199 89% 48%;
    --info-foreground: 0 0% 98%;

    /* Premium gradients */
    --gradient-primary: linear-gradient(135deg, hsl(262 83% 58%) 0%, hsl(262 83% 48%) 100%);
    --gradient-secondary: linear-gradient(135deg, hsl(240 4.8% 95.9%) 0%, hsl(240 4.8% 90%) 100%);
    --gradient-background: linear-gradient(135deg, hsl(250 250% 99%) 0%, hsl(240 4.8% 97%) 100%);
    --gradient-mesh: radial-gradient(at 40% 20%, hsl(262 83% 58% / 0.05) 0px, transparent 50%),
                     radial-gradient(at 80% 0%, hsl(199 89% 48% / 0.05) 0px, transparent 50%),
                     radial-gradient(at 0% 50%, hsl(142 71% 45% / 0.05) 0px, transparent 50%);

    /* Modern shadows */
    --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-md: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25), 0 0 0 1px rgb(255 255 255 / 0.05);

    /* Refined spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;

    /* Modern border radius */
    --radius-xs: 0.125rem;
    --radius-sm: 0.25rem;
    --radius: 0.5rem;
    --radius-md: 0.75rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;
    --radius-2xl: 2rem;

    /* Smooth transitions */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 250ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 350ms cubic-bezier(0.4, 0, 0.2, 1);

    /* Layout */
    --sidebar-width: 280px;
    --sidebar-width-collapsed: 80px;
    --header-height: 64px;
}

/* Dark theme - Inspired by GitHub Dark, Linear Dark */
[data-theme="dark"] {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;

    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 262 83% 58%;
    --primary-foreground: 240 10% 3.9%;

    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 262 83% 58%;

    --gradient-background: linear-gradient(135deg, hsl(240 10% 3.9%) 0%, hsl(240 3.7% 8%) 100%);
    --gradient-mesh: radial-gradient(at 40% 20%, hsl(262 83% 58% / 0.1) 0px, transparent 50%),
                     radial-gradient(at 80% 0%, hsl(199 89% 48% / 0.1) 0px, transparent 50%),
                     radial-gradient(at 0% 50%, hsl(142 71% 45% / 0.1) 0px, transparent 50%);
}

/* --- Modern Reset and Base Styles --- */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
    line-height: 1.5;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    line-height: 1.6;
    color: hsl(var(--foreground));
    background: var(--gradient-background);
    background-attachment: fixed;
    min-height: 100vh;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
    font-variation-settings: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    overflow-x: hidden;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-mesh);
    z-index: -1;
    pointer-events: none;
}

/* --- Modern Layout Structure --- */
.app-layout {
    display: flex;
    min-height: 100vh;
    position: relative;
}

.sidebar {
    width: var(--sidebar-width);
    background: hsl(var(--card) / 0.8);
    backdrop-filter: blur(20px);
    border-right: 1px solid hsl(var(--border) / 0.5);
    box-shadow: var(--shadow-lg);
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    z-index: 50;
    transition: all var(--transition-normal);
    overflow: hidden;
}

.sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg, hsl(var(--primary) / 0.02) 0%, transparent 100%);
    pointer-events: none;
}

.sidebar.collapsed {
    width: var(--sidebar-width-collapsed);
}

.sidebar.collapsed .sidebar-header {
    justify-content: center;
    padding: var(--spacing-xl) var(--spacing-md);
    flex-direction: column;
    gap: var(--spacing-lg);
    border-bottom: 1px solid hsl(var(--border) / 0.3);
}

.sidebar.collapsed .sidebar-header::after {
    display: none;
}

.sidebar.collapsed .sidebar-logo {
    display: none;
}

/* Collapsed sidebar brand icon */
.sidebar.collapsed .sidebar-header::before {
    content: '⚡';
    font-size: 1.5rem;
    width: 48px;
    height: 48px;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-lg);
    margin-bottom: var(--spacing-sm);
}

.sidebar.collapsed .sidebar-toggle {
    margin-left: 0;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.125rem;
    background: hsl(var(--muted) / 0.5);
    color: hsl(var(--muted-foreground));
    border: 1px solid hsl(var(--border) / 0.5);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
}

.sidebar.collapsed .sidebar-toggle:hover {
    transform: scale(1.05);
    background: hsl(var(--muted));
    color: hsl(var(--foreground));
    border-color: hsl(var(--border));
    box-shadow: var(--shadow-md);
}

.sidebar-header {
    padding: var(--spacing-xl) var(--spacing-lg);
    border-bottom: 1px solid hsl(var(--border) / 0.5);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    position: relative;
}

.sidebar-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: var(--spacing-lg);
    right: var(--spacing-lg);
    height: 1px;
    background: linear-gradient(90deg, transparent, hsl(var(--border)), transparent);
}

.sidebar-logo {
    font-size: 1.5rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-decoration: none;
    transition: all var(--transition-fast);
    letter-spacing: -0.025em;
}

.sidebar-logo:hover {
    transform: scale(1.02);
}

.sidebar.collapsed .sidebar-logo {
    opacity: 0;
    transform: scale(0.8);
}

.sidebar-toggle {
    background: hsl(var(--muted) / 0.5);
    border: 1px solid hsl(var(--border) / 0.5);
    color: hsl(var(--muted-foreground));
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    margin-left: auto;
    backdrop-filter: blur(10px);
}

.sidebar-toggle:hover {
    background: hsl(var(--muted));
    color: hsl(var(--foreground));
    border-color: hsl(var(--border));
    transform: scale(1.05);
}

/* Premium floating toggle button for collapsed sidebar */
.floating-toggle {
    display: none;
    position: fixed;
    top: var(--spacing-xl);
    left: var(--spacing-xl);
    z-index: 60;
    background: var(--gradient-primary);
    color: hsl(var(--primary-foreground));
    border: none;
    border-radius: 50%;
    width: 56px;
    height: 56px;
    cursor: pointer;
    box-shadow: var(--shadow-2xl);
    transition: all var(--transition-fast);
    font-size: 1.25rem;
    backdrop-filter: blur(10px);
}

.floating-toggle::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, transparent 100%);
    pointer-events: none;
}

.floating-toggle:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: var(--shadow-2xl), 0 0 20px hsl(var(--primary) / 0.3);
}

.floating-toggle.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    transition: all var(--transition-normal);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
}

.main-content.sidebar-collapsed {
    margin-left: var(--sidebar-width-collapsed);
}

.content-header {
    background: hsl(var(--card) / 0.8);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid hsl(var(--border) / 0.5);
    padding: var(--spacing-lg) var(--spacing-2xl);
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 40;
    height: var(--header-height);
}

.content-header h1 {
    font-size: 1.75rem;
    font-weight: 600;
    background: linear-gradient(135deg, hsl(var(--foreground)) 0%, hsl(var(--muted-foreground)) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.025em;
}

.content-body {
    flex: 1;
    padding: var(--spacing-2xl);
    background: transparent;
    position: relative;
}

main {
    background: hsl(var(--card) / 0.7);
    backdrop-filter: blur(20px);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    border: 1px solid hsl(var(--border) / 0.5);
    padding: var(--spacing-2xl);
    margin-bottom: var(--spacing-xl);
    position: relative;
    overflow: hidden;
}

main::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, hsl(var(--primary) / 0.3), transparent);
}

/* --- Typography --- */
h1, h2, h3, h4, h5, h6 {
    color: hsl(var(--foreground));
    margin-bottom: var(--spacing-lg);
    font-weight: 600;
    line-height: 1.2;
}

h1 {
    font-size: 2.5rem;
    font-weight: 700;
}

h2 {
    font-size: 2rem;
    border-bottom: 1px solid hsl(var(--border));
    padding-bottom: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

h3 {
    font-size: 1.5rem;
}

h4 {
    font-size: 1.25rem;
}

p {
    margin-bottom: var(--spacing-md);
    color: hsl(var(--muted-foreground));
}

/* --- Modern Sidebar Navigation --- */
.sidebar-nav {
    padding: var(--spacing-lg) 0;
    height: calc(100vh - 120px);
    overflow-y: auto;
    overflow-x: hidden;
}

.sidebar-nav::-webkit-scrollbar {
    width: 4px;
}

.sidebar-nav::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar-nav::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.3);
    border-radius: 2px;
}

.nav-section {
    margin-bottom: var(--spacing-xl);
}

.nav-section-title {
    font-size: 0.6875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    color: hsl(var(--muted-foreground) / 0.7);
    padding: 0 var(--spacing-lg);
    margin-bottom: var(--spacing-md);
    transition: all var(--transition-fast);
    position: relative;
}

.nav-section-title::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: var(--spacing-lg);
    width: 20px;
    height: 1px;
    background: hsl(var(--muted-foreground) / 0.2);
    transition: all var(--transition-fast);
}

.sidebar.collapsed .nav-section-title {
    opacity: 0;
    transform: translateX(-10px);
}

.nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.nav-item {
    position: relative;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    color: hsl(var(--muted-foreground));
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all var(--transition-fast);
    border-radius: var(--radius-lg);
    margin: 0 var(--spacing-md);
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, hsl(var(--primary) / 0.1) 0%, hsl(var(--primary) / 0.05) 100%);
    opacity: 0;
    transition: all var(--transition-fast);
}

.nav-link:hover {
    color: hsl(var(--foreground));
    transform: translateX(2px);
}

.nav-link:hover::before {
    opacity: 1;
}

.nav-link.active {
    background: var(--gradient-primary);
    color: hsl(var(--primary-foreground));
    box-shadow: var(--shadow-md);
    transform: translateX(2px);
}

.nav-link.active::before {
    opacity: 0;
}

.nav-icon {
    width: 18px;
    height: 18px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    transition: all var(--transition-fast);
}

.nav-text {
    transition: all var(--transition-fast);
    font-weight: 500;
}

.sidebar.collapsed .nav-text {
    display: none;
}

.sidebar.collapsed .nav-link {
    justify-content: center;
    padding: var(--spacing-md);
    margin: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-lg);
    width: 48px;
    height: 48px;
    position: relative;
    overflow: visible;
}

.sidebar.collapsed .nav-link:hover {
    transform: scale(1.1);
}

.sidebar.collapsed .nav-link.active {
    background: var(--gradient-primary);
    color: hsl(var(--primary-foreground));
    box-shadow: var(--shadow-lg);
    transform: scale(1.05);
}

.sidebar.collapsed .nav-icon {
    font-size: 1.25rem;
}

/* Tooltip for collapsed sidebar */
.sidebar.collapsed .nav-link::after {
    content: attr(data-tooltip);
    position: absolute;
    left: calc(100% + var(--spacing-md));
    top: 50%;
    transform: translateY(-50%);
    background: hsl(var(--card));
    color: hsl(var(--foreground));
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    white-space: nowrap;
    box-shadow: var(--shadow-lg);
    border: 1px solid hsl(var(--border));
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-fast);
    z-index: 1000;
    pointer-events: none;
}

.sidebar.collapsed .nav-link:hover::after {
    opacity: 1;
    visibility: visible;
    transform: translateY(-50%) translateX(4px);
}

.sidebar.collapsed .nav-section-title {
    display: none;
}

.sidebar.collapsed .nav-section {
    margin-bottom: var(--spacing-md);
}

.sidebar.collapsed .nav-section::before {
    content: '';
    display: block;
    width: 24px;
    height: 1px;
    background: linear-gradient(90deg, transparent, hsl(var(--border) / 0.5), transparent);
    margin: var(--spacing-lg) auto;
}

.sidebar.collapsed .nav-section:first-child::before {
    display: none;
}

/* Smooth transition for collapsed state */
.sidebar.collapsed .nav-link {
    animation: slideInCollapsed 0.3s ease-out;
}

@keyframes slideInCollapsed {
    from {
        opacity: 0;
        transform: translateX(-10px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

/* Better spacing for collapsed navigation */
.sidebar.collapsed .sidebar-nav {
    padding: var(--spacing-md) 0;
}

nav ul li.active:hover a {
    color: hsl(var(--primary-foreground));
}

/* --- Premium Button System --- */
.button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    border: 1px solid transparent;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);

    /* Default (Primary) */
    background: var(--gradient-primary);
    color: hsl(var(--primary-foreground));
    box-shadow: var(--shadow), 0 0 0 1px hsl(var(--primary) / 0.1);
}

.button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left var(--transition-slow);
}

.button:hover::before {
    left: 100%;
}

.button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg), 0 0 0 1px hsl(var(--primary) / 0.2);
}

.button:active {
    transform: translateY(-1px);
    box-shadow: var(--shadow), 0 0 0 1px hsl(var(--primary) / 0.1);
}

.button:focus-visible {
    outline: none;
    box-shadow: var(--shadow), 0 0 0 3px hsl(var(--ring) / 0.3);
}

/* Button Variants */
.button-secondary {
    background: hsl(var(--secondary) / 0.8);
    color: hsl(var(--secondary-foreground));
    border: 1px solid hsl(var(--border) / 0.5);
    box-shadow: var(--shadow-sm), 0 0 0 1px hsl(var(--border) / 0.1);
}

.button-secondary:hover {
    background: hsl(var(--secondary));
    border-color: hsl(var(--border));
    box-shadow: var(--shadow-md), 0 0 0 1px hsl(var(--border) / 0.2);
}

.button-outline {
    background: hsl(var(--card) / 0.5);
    color: hsl(var(--foreground));
    border: 1px solid hsl(var(--border) / 0.5);
    box-shadow: var(--shadow-sm);
    backdrop-filter: blur(10px);
}

.button-outline:hover {
    background: hsl(var(--muted) / 0.8);
    border-color: hsl(var(--border));
    box-shadow: var(--shadow-md);
}

.button-ghost {
    background: transparent;
    color: hsl(var(--foreground));
    border: none;
    box-shadow: none;
}

.button-ghost:hover {
    background: hsl(var(--muted) / 0.5);
    box-shadow: var(--shadow-sm);
}

.button-destructive {
    background: linear-gradient(135deg, hsl(var(--destructive)) 0%, hsl(0 84.2% 50%) 100%);
    color: hsl(var(--destructive-foreground));
    box-shadow: var(--shadow), 0 0 0 1px hsl(var(--destructive) / 0.1);
}

.button-destructive:hover {
    box-shadow: var(--shadow-lg), 0 0 0 1px hsl(var(--destructive) / 0.2);
}

.button-success {
    background: linear-gradient(135deg, hsl(var(--success)) 0%, hsl(142 71% 35%) 100%);
    color: hsl(var(--success-foreground));
    box-shadow: var(--shadow), 0 0 0 1px hsl(var(--success) / 0.1);
}

.button-success:hover {
    box-shadow: var(--shadow-lg), 0 0 0 1px hsl(var(--success) / 0.2);
}

.button-warning {
    background: linear-gradient(135deg, hsl(var(--warning)) 0%, hsl(38 92% 40%) 100%);
    color: hsl(var(--warning-foreground));
    box-shadow: var(--shadow), 0 0 0 1px hsl(var(--warning) / 0.1);
}

.button-warning:hover {
    box-shadow: var(--shadow-lg), 0 0 0 1px hsl(var(--warning) / 0.2);
}

/* Button Sizes */
.button-sm {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.875rem;
}

.button-lg {
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: 1.125rem;
}

.button-icon {
    padding: var(--spacing-md);
    width: 40px;
    height: 40px;
}

/* --- Modern Form System --- */
.form-group {
    margin-bottom: var(--spacing-lg);
    position: relative;
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: hsl(var(--foreground));
    font-size: 0.9375rem;
    transition: color var(--transition-fast);
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-md);
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
    font-size: 1rem;
    color: hsl(var(--foreground));
    background-color: hsl(var(--background));
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    border-color: hsl(var(--primary));
    box-shadow: 0 0 0 3px hsl(var(--primary) / 0.1);
    outline: none;
}

.form-input:hover,
.form-select:hover,
.form-textarea:hover {
    border-color: hsl(var(--muted-foreground));
}

/* Legacy form support */
form div {
    margin-bottom: var(--spacing-lg);
}

form label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: hsl(var(--foreground));
    font-size: 0.9375rem;
}

form input[type="text"],
form input[type="email"],
form input[type="number"],
form input[type="password"],
form select,
form textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
    font-size: 1rem;
    color: hsl(var(--foreground));
    background-color: hsl(var(--background));
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

form input:focus,
form select:focus,
form textarea:focus {
    border-color: hsl(var(--primary));
    box-shadow: 0 0 0 3px hsl(var(--primary) / 0.1);
    outline: none;
}

form input:hover,
form select:hover,
form textarea:hover {
    border-color: hsl(var(--muted-foreground));
}

/* Input states */
.form-input.error,
form input.error {
    border-color: hsl(var(--destructive));
    box-shadow: 0 0 0 3px hsl(var(--destructive) / 0.1);
}

.form-input.success,
form input.success {
    border-color: hsl(var(--success));
    box-shadow: 0 0 0 3px hsl(var(--success) / 0.1);
}

.form-help-text {
    font-size: 0.875rem;
    color: hsl(var(--muted-foreground));
    margin-top: var(--spacing-xs);
}

.form-error-text {
    font-size: 0.875rem;
    color: hsl(var(--destructive));
    margin-top: var(--spacing-xs);
}

/* --- Premium Card System --- */
.card {
    background: hsl(var(--card) / 0.7);
    backdrop-filter: blur(20px);
    border: 1px solid hsl(var(--border) / 0.5);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    transition: all var(--transition-fast);
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, hsl(var(--primary) / 0.3), transparent);
}

.card:hover {
    box-shadow: var(--shadow-2xl);
    transform: translateY(-4px);
    border-color: hsl(var(--border));
}

.card-header {
    padding: var(--spacing-2xl) var(--spacing-2xl) var(--spacing-lg);
    border-bottom: 1px solid hsl(var(--border) / 0.3);
    background: linear-gradient(135deg, hsl(var(--muted) / 0.1) 0%, transparent 100%);
    position: relative;
}

.card-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: var(--spacing-2xl);
    right: var(--spacing-2xl);
    height: 1px;
    background: linear-gradient(90deg, transparent, hsl(var(--border) / 0.5), transparent);
}

.card-title {
    font-size: 1.5rem;
    font-weight: 600;
    background: linear-gradient(135deg, hsl(var(--foreground)) 0%, hsl(var(--muted-foreground)) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    letter-spacing: -0.025em;
}

.card-description {
    font-size: 0.875rem;
    color: hsl(var(--muted-foreground) / 0.8);
    margin: var(--spacing-sm) 0 0 0;
    line-height: 1.5;
}

.card-content {
    padding: var(--spacing-2xl);
}

.card-footer {
    padding: var(--spacing-lg) var(--spacing-2xl) var(--spacing-2xl);
    border-top: 1px solid hsl(var(--border) / 0.3);
    background: linear-gradient(135deg, hsl(var(--muted) / 0.05) 0%, transparent 100%);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
}

.card-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: var(--spacing-2xl);
    right: var(--spacing-2xl);
    height: 1px;
    background: linear-gradient(90deg, transparent, hsl(var(--border) / 0.5), transparent);
}

/* --- Premium Stats Cards --- */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-2xl);
}

.stat-card {
    background: hsl(var(--card) / 0.7);
    backdrop-filter: blur(20px);
    border: 1px solid hsl(var(--border) / 0.5);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-2xl);
    box-shadow: var(--shadow-xl);
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
}

.stat-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 80% 20%, hsl(var(--primary) / 0.05) 0%, transparent 50%);
    pointer-events: none;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-2xl);
    border-color: hsl(var(--border));
}

.stat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
    position: relative;
    z-index: 1;
}

.stat-title {
    font-size: 0.75rem;
    font-weight: 600;
    color: hsl(var(--muted-foreground) / 0.8);
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.stat-icon {
    width: 32px;
    height: 32px;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.125rem;
    box-shadow: var(--shadow-md);
}

.stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, hsl(var(--foreground)) 0%, hsl(var(--muted-foreground)) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-sm);
    line-height: 1.1;
    position: relative;
    z-index: 1;
}

.stat-change {
    font-size: 0.875rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    backdrop-filter: blur(10px);
    position: relative;
    z-index: 1;
}

.stat-change.positive {
    color: hsl(var(--success));
    background: hsl(var(--success) / 0.1);
    border: 1px solid hsl(var(--success) / 0.2);
}

.stat-change.negative {
    color: hsl(var(--destructive));
    background: hsl(var(--destructive) / 0.1);
    border: 1px solid hsl(var(--destructive) / 0.2);
}

/* --- Utility Classes --- */
.flex {
    display: flex;
}

.flex-col {
    flex-direction: column;
}

.items-center {
    align-items: center;
}

.justify-between {
    justify-content: space-between;
}

.gap-2 {
    gap: var(--spacing-sm);
}

.gap-4 {
    gap: var(--spacing-md);
}

.mb-4 {
    margin-bottom: var(--spacing-md);
}

.mb-6 {
    margin-bottom: var(--spacing-lg);
}

.text-sm {
    font-size: 0.875rem;
}

.text-muted {
    color: hsl(var(--muted-foreground));
}

.font-medium {
    font-weight: 500;
}

.font-semibold {
    font-weight: 600;
}

/* --- Modern Theme Toggle --- */
.theme-toggle {
    position: fixed;
    top: var(--spacing-xl);
    right: var(--spacing-xl);
    z-index: 100;
    background: hsl(var(--card) / 0.8);
    backdrop-filter: blur(20px);
    border: 1px solid hsl(var(--border) / 0.5);
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-fast);
    font-size: 1.25rem;
}

.theme-toggle:hover {
    box-shadow: var(--shadow-xl);
    transform: translateY(-2px) scale(1.05);
    border-color: hsl(var(--border));
    background: hsl(var(--card));
}

/* Specific form adjustments for order items */
.order-item {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr auto;
    gap: 1rem;
    align-items: flex-end;
    margin-bottom: 1rem;
    padding: 1rem;
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
    background-color: hsl(var(--muted) / 0.5);
}

.order-item .remove-item {
    background-color: hsl(var(--destructive));
    border-color: hsl(var(--destructive));
    color: hsl(var(--destructive-foreground));
    padding: 0.625rem 0.75rem;
    font-size: 0.875rem;
    height: fit-content;
}

.order-item .remove-item:hover {
    background-color: hsl(var(--destructive) / 0.9);
    border-color: hsl(var(--destructive) / 0.9);
}

/* --- Premium Data Tables --- */
.table-container {
    background: hsl(var(--card) / 0.7);
    backdrop-filter: blur(20px);
    border: 1px solid hsl(var(--border) / 0.5);
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: var(--shadow-xl);
    position: relative;
}

.table-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, hsl(var(--primary) / 0.3), transparent);
}

.table-header {
    padding: var(--spacing-xl) var(--spacing-2xl);
    border-bottom: 1px solid hsl(var(--border) / 0.5);
    background: linear-gradient(135deg, hsl(var(--muted) / 0.2) 0%, hsl(var(--muted) / 0.1) 100%);
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: var(--spacing-lg);
    position: relative;
}

.table-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: var(--spacing-2xl);
    right: var(--spacing-2xl);
    height: 1px;
    background: linear-gradient(90deg, transparent, hsl(var(--border)), transparent);
}

.table-title {
    font-size: 1.5rem;
    font-weight: 600;
    background: linear-gradient(135deg, hsl(var(--foreground)) 0%, hsl(var(--muted-foreground)) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    letter-spacing: -0.025em;
}

.table-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.table-search {
    position: relative;
}

.table-search input {
    padding: var(--spacing-md) var(--spacing-lg);
    padding-left: 3rem;
    border: 1px solid hsl(var(--border) / 0.5);
    border-radius: var(--radius-lg);
    background: hsl(var(--card) / 0.5);
    backdrop-filter: blur(10px);
    color: hsl(var(--foreground));
    font-size: 0.875rem;
    width: 280px;
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

.table-search input:focus {
    border-color: hsl(var(--primary));
    box-shadow: var(--shadow-md), 0 0 0 3px hsl(var(--primary) / 0.1);
    outline: none;
    transform: translateY(-1px);
}

.table-search input::placeholder {
    color: hsl(var(--muted-foreground) / 0.7);
}

.table-search::before {
    content: '🔍';
    position: absolute;
    left: var(--spacing-lg);
    top: 50%;
    transform: translateY(-50%);
    color: hsl(var(--muted-foreground));
    font-size: 0.875rem;
}

.data-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.data-table th,
.data-table td {
    padding: var(--spacing-lg) var(--spacing-2xl);
    text-align: left;
    border-bottom: 1px solid hsl(var(--border) / 0.3);
}

.data-table th {
    background: linear-gradient(135deg, hsl(var(--muted) / 0.3) 0%, hsl(var(--muted) / 0.1) 100%);
    font-weight: 600;
    color: hsl(var(--muted-foreground));
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    position: sticky;
    top: 0;
    z-index: 10;
    backdrop-filter: blur(10px);
}

.data-table tbody tr {
    transition: all var(--transition-fast);
    position: relative;
}

.data-table tbody tr:hover {
    background: linear-gradient(135deg, hsl(var(--muted) / 0.1) 0%, hsl(var(--primary) / 0.02) 100%);
    transform: translateY(-1px);
}

.data-table tbody tr:hover td {
    border-color: hsl(var(--border) / 0.5);
}

.data-table tbody tr:last-child td {
    border-bottom: none;
}

/* Table action buttons */
.table-actions-cell {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.action-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: var(--radius);
    text-decoration: none;
    transition: all var(--transition-fast);
    border: 1px solid transparent;
}

.action-button.edit {
    background: hsl(var(--primary) / 0.1);
    color: hsl(var(--primary));
    border-color: hsl(var(--primary) / 0.2);
}

.action-button.edit:hover {
    background: hsl(var(--primary) / 0.2);
}

.action-button.delete {
    background: hsl(var(--destructive) / 0.1);
    color: hsl(var(--destructive));
    border-color: hsl(var(--destructive) / 0.2);
}

.action-button.delete:hover {
    background: hsl(var(--destructive) / 0.2);
}

.action-button.view {
    background: hsl(var(--muted));
    color: hsl(var(--muted-foreground));
}

.action-button.view:hover {
    background: hsl(var(--muted) / 0.8);
    color: hsl(var(--foreground));
}

/* Table pagination */
.table-pagination {
    padding: var(--spacing-lg);
    border-top: 1px solid hsl(var(--border));
    background: hsl(var(--muted) / 0.3);
    display: flex;
    align-items: center;
    justify-content: between;
}

.pagination-info {
    color: hsl(var(--muted-foreground));
    font-size: 0.875rem;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-left: auto;
}

/* Legacy table support */
table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-top: var(--spacing-lg);
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius-lg);
    overflow: hidden;
    background: hsl(var(--card));
    box-shadow: var(--shadow);
}

table th,
table td {
    padding: var(--spacing-md) var(--spacing-lg);
    text-align: left;
    border-bottom: 1px solid hsl(var(--border));
}

table th {
    background: hsl(var(--muted) / 0.5);
    font-weight: 600;
    color: hsl(var(--foreground));
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

table tbody tr:last-child td {
    border-bottom: none;
}

table tbody tr:hover {
    background: hsl(var(--muted) / 0.3);
}

/* Table actions links */
table td a {
    margin-right: var(--spacing-md);
    font-size: 0.875rem;
    color: hsl(var(--primary));
    text-decoration: none;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius);
    transition: all var(--transition-fast);
}

table td a:hover {
    background: hsl(var(--primary) / 0.1);
    color: hsl(var(--primary));
}

/* Footer for tables (e.g., total) */
table tfoot td {
    font-weight: 600;
    background: hsl(var(--muted));
    border-top: 1px solid hsl(var(--border));
    color: hsl(var(--foreground));
}

/* --- Responsive Design --- */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }

    main {
        padding: 20px;
        margin: 10px auto;
    }

    nav ul {
        flex-direction: column;
        gap: 0.5rem;
        padding: 0 10px;
    }

    nav ul li a {
        display: block;
        text-align: center;
    }

    .order-item {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .order-item div {
        margin-bottom: 0;
    }

    table,
    thead,
    tbody,
    th,
    td,
    tr {
        display: block;
    }

    thead tr {
        position: absolute;
        top: -9999px;
        left: -9999px;
    }

    tr {
        border: 1px solid hsl(var(--border));
        margin-bottom: 10px;
        border-radius: var(--radius);
        overflow: hidden;
    }

    td {
        border: none;
        border-bottom: 1px solid hsl(var(--border));
        position: relative;
        padding-left: 50%;
        text-align: right;
    }

    td:last-child {
        border-bottom: none;
    }

    td:before {
        position: absolute;
        top: 0;
        left: 6px;
        width: 45%;
        padding-right: 10px;
        white-space: nowrap;
        text-align: left;
        font-weight: 600;
        color: hsl(var(--muted-foreground));
    }

    /* Label the data */
    td:nth-of-type(1):before { content: "ID:"; }
    td:nth-of-type(2):before { content: "Name:"; }
    td:nth-of-type(3):before { content: "Email:"; }
    td:nth-of-type(4):before { content: "Actions:"; }

    /* Specific for products table */
    .products-table td:nth-of-type(1):before { content: "ID:"; }
    .products-table td:nth-of-type(2):before { content: "Name:"; }
    .products-table td:nth-of-type(3):before { content: "SKU:"; }
    .products-table td:nth-of-type(4):before { content: "Quantity:"; }
    .products-table td:nth-of-type(5):before { content: "Price:"; }
    .products-table td:nth-of-type(6):before { content: "Actions:"; }

    /* Specific for sales table */
    .sales-table td:nth-of-type(1):before { content: "Order ID:"; }
    .sales-table td:nth-of-type(2):before { content: "Customer:"; }
    .sales-table td:nth-of-type(3):before { content: "Order Date:"; }
    .sales-table td:nth-of-type(4):before { content: "Actions:"; }

    /* Specific for order items table */
    .order-items-table td:nth-of-type(1):before { content: "Product Name:"; }
    .order-items-table td:nth-of-type(2):before { content: "SKU:"; }
    .order-items-table td:nth-of-type(3):before { content: "Quantity:"; }
    .order-items-table td:nth-of-type(4):before { content: "Price:"; }
    .order-items-table td:nth-of-type(5):before { content: "Total:"; }

    table tfoot td {
        text-align: right !important;
        padding-left: 6px;
    }
}
/* --- Modern Login Page Styles --- */
.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: var(--gradient-background);
    padding: var(--spacing-lg);
    position: relative;
    overflow: hidden;
}

.login-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, hsl(var(--primary) / 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, hsl(var(--primary) / 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, hsl(var(--primary) / 0.05) 0%, transparent 50%);
    z-index: 1;
}

.login-card {
    background: hsl(var(--card));
    backdrop-filter: blur(10px);
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    width: 100%;
    max-width: 420px;
    padding: var(--spacing-2xl);
    position: relative;
    z-index: 2;
    overflow: hidden;
}

.login-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    z-index: 1;
}

.login-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.login-logo {
    width: 72px;
    height: 72px;
    margin: 0 auto var(--spacing-lg);
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    box-shadow: var(--shadow-lg);
    animation: pulse 2s infinite;
}

.login-title {
    font-size: 2.25rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-sm);
    line-height: 1.2;
}

.login-subtitle {
    color: hsl(var(--muted-foreground));
    font-size: 1rem;
    margin: 0;
}

.login-form {
    margin-bottom: var(--spacing-xl);
}

.login-form .form-group {
    margin-bottom: var(--spacing-lg);
    text-align: left;
}

.login-form .form-label {
    display: block;
    font-size: 0.9375rem;
    font-weight: 500;
    color: hsl(var(--foreground));
    margin-bottom: var(--spacing-sm);
}

.login-input {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 1rem;
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius-lg);
    background: hsl(var(--background));
    color: hsl(var(--foreground));
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

.login-input:focus {
    border-color: hsl(var(--primary));
    box-shadow: 0 0 0 3px hsl(var(--primary) / 0.1);
    outline: none;
    transform: translateY(-1px);
}

.login-input:hover {
    border-color: hsl(var(--muted-foreground));
}

.login-button {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 1rem;
    font-weight: 600;
    background: var(--gradient-primary);
    color: hsl(var(--primary-foreground));
    border: none;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
    box-shadow: var(--shadow);
    position: relative;
    overflow: hidden;
    margin-top: var(--spacing-lg);
}

.login-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left var(--transition-slow);
}

.login-button:hover::before {
    left: 100%;
}

.login-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.login-button:active {
    transform: translateY(0);
}

.login-error {
    background: hsl(var(--destructive) / 0.1);
    border: 1px solid hsl(var(--destructive) / 0.3);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: hsl(var(--destructive));
    font-size: 0.9375rem;
    animation: fadeIn 0.3s ease-out;
}

.login-footer {
    text-align: center;
    padding-top: var(--spacing-lg);
    border-top: 1px solid hsl(var(--border));
    color: hsl(var(--muted-foreground));
    font-size: 0.875rem;
}

.login-footer-icon {
    margin-right: var(--spacing-xs);
}

/* Dark mode adjustments for login */
[data-theme="dark"] .login-card {
    background: hsl(var(--card) / 0.95);
    backdrop-filter: blur(20px);
}

[data-theme="dark"] .login-container::before {
    background:
        radial-gradient(circle at 20% 80%, hsl(var(--primary) / 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, hsl(var(--primary) / 0.05) 0%, transparent 50%);
}

/* Floating label effect for login */
.login-form .form-group {
    position: relative;
}

.login-form .form-group.focused .form-label {
    transform: translateY(-8px) scale(0.85);
    color: hsl(var(--primary));
}

.login-form .form-label {
    transition: all var(--transition-fast);
    transform-origin: left top;
}

/* Loading state for login button */
.login-button.loading {
    pointer-events: none;
    opacity: 0.8;
}

.login-button.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    right: var(--spacing-lg);
    width: 16px;
    height: 16px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Enhanced input states */
.login-input:invalid:not(:focus):not(:placeholder-shown) {
    border-color: hsl(var(--destructive));
    box-shadow: 0 0 0 3px hsl(var(--destructive) / 0.1);
}

.login-input:valid:not(:focus):not(:placeholder-shown) {
    border-color: hsl(var(--success));
    box-shadow: 0 0 0 3px hsl(var(--success) / 0.1);
}

/* Pulse animation for logo */
@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: var(--shadow-lg);
    }
    50% {
        transform: scale(1.05);
        box-shadow: var(--shadow-xl);
    }
}

/* Mobile responsiveness for login */
@media (max-width: 480px) {
    .login-container {
        padding: var(--spacing-md);
    }

    .login-card {
        padding: var(--spacing-xl);
        max-width: 100%;
    }

    .login-title {
        font-size: 1.875rem;
    }

    .login-logo {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .login-input {
        padding: var(--spacing-md);
        font-size: 16px; /* Prevent zoom on iOS */
    }
}

/* --- Interactive Elements & Animations --- */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes shimmer {
    0% { background-position: -200px 0; }
    100% { background-position: calc(200px + 100%) 0; }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.animate-fade-in {
    animation: fadeIn 0.5s ease-out;
}

.animate-slide-in {
    animation: slideIn 0.3s ease-out;
}

.loading-skeleton {
    background: linear-gradient(90deg, hsl(var(--muted)) 25%, hsl(var(--muted) / 0.5) 50%, hsl(var(--muted)) 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* Hover effects */
.hover-lift {
    transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Focus states */
.focus-ring:focus-visible {
    outline: none;
    box-shadow: 0 0 0 3px hsl(var(--ring) / 0.3);
}

/* Loading states */
.loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid hsl(var(--muted));
    border-top: 2px solid hsl(var(--primary));
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Notification styles */
.notification {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    background: hsl(var(--card));
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-xl);
    z-index: 1000;
    animation: slideIn 0.3s ease-out;
    max-width: 400px;
}

.notification.success {
    border-left: 4px solid hsl(var(--success));
}

.notification.error {
    border-left: 4px solid hsl(var(--destructive));
}

.notification.warning {
    border-left: 4px solid hsl(var(--warning));
}

/* Mobile menu button */
.mobile-menu-button {
    display: none;
    position: fixed;
    top: var(--spacing-lg);
    left: var(--spacing-lg);
    z-index: 100;
    background: hsl(var(--card));
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
    padding: var(--spacing-md);
    cursor: pointer;
    box-shadow: var(--shadow);
    transition: all var(--transition-fast);
}

.mobile-menu-button:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.mobile-menu-button span {
    font-size: 1.25rem;
}

/* Mobile overlay */
.mobile-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 60;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.mobile-overlay.active {
    opacity: 1;
}

/* Enhanced mobile responsiveness */
@media (max-width: 1024px) {
    .mobile-menu-button {
        display: block;
    }

    .mobile-overlay {
        display: block;
    }

    .sidebar {
        transform: translateX(-100%);
        transition: transform var(--transition-normal);
        z-index: 70;
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .content-header {
        padding-left: 80px; /* Make room for mobile menu button */
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .table-header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-md);
    }

    .table-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .table-search input {
        width: 100%;
    }
}

@media (max-width: 768px) {
    .content-header {
        padding: var(--spacing-md);
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }

    .content-body {
        padding: var(--spacing-md);
    }

    main {
        padding: var(--spacing-md);
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .card-footer {
        flex-direction: column;
        align-items: stretch;
    }

    .card-footer .flex {
        flex-direction: column;
    }

    .data-table {
        font-size: 0.875rem;
    }

    .data-table th,
    .data-table td {
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .table-actions-cell {
        flex-direction: column;
        align-items: stretch;
    }

    .action-button {
        justify-content: center;
        width: 100%;
    }
}

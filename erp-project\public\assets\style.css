/* public/assets/style.css */

/* --- Enhanced Shadcn Design System --- */
:root {
    /* Light theme colors */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;

    /* Enhanced color palette */
    --success: 142.1 76.2% 36.3%;
    --success-foreground: 355.7 100% 97.3%;
    --warning: 47.9 95.8% 53.1%;
    --warning-foreground: 26 83.3% 14.1%;
    --info: 199.89 89.09% 48.04%;
    --info-foreground: 210 40% 98%;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(221.2 83.2% 45%) 100%);
    --gradient-secondary: linear-gradient(135deg, hsl(var(--secondary)) 0%, hsl(210 40% 90%) 100%);
    --gradient-background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(210 40% 98%) 100%);

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* Border radius */
    --radius-sm: 0.25rem;
    --radius: 0.5rem;
    --radius-md: 0.75rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;

    /* Sidebar */
    --sidebar-width: 280px;
    --sidebar-width-collapsed: 80px;
}

/* Dark theme */
[data-theme="dark"] {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;

    --gradient-background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(217.2 32.6% 8%) 100%);
}

/* --- Reset and Base Styles --- */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    line-height: 1.6;
    color: hsl(var(--foreground));
    background: var(--gradient-background);
    min-height: 100vh;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
    font-variation-settings: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* --- Layout Structure --- */
.app-layout {
    display: flex;
    min-height: 100vh;
}

.sidebar {
    width: var(--sidebar-width);
    background: hsl(var(--card));
    border-right: 1px solid hsl(var(--border));
    box-shadow: var(--shadow-sm);
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    z-index: 50;
    transition: width var(--transition-normal);
    overflow: hidden;
}

.sidebar.collapsed {
    width: var(--sidebar-width-collapsed);
}

.sidebar-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid hsl(var(--border));
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.sidebar-logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: hsl(var(--primary));
    text-decoration: none;
    transition: opacity var(--transition-fast);
}

.sidebar.collapsed .sidebar-logo {
    opacity: 0;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: hsl(var(--muted-foreground));
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius);
    transition: all var(--transition-fast);
    margin-left: auto;
}

.sidebar-toggle:hover {
    background: hsl(var(--muted));
    color: hsl(var(--foreground));
}

.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    transition: margin-left var(--transition-normal);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.main-content.sidebar-collapsed {
    margin-left: var(--sidebar-width-collapsed);
}

.content-header {
    background: hsl(var(--card));
    border-bottom: 1px solid hsl(var(--border));
    padding: var(--spacing-lg) var(--spacing-xl);
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: var(--shadow-sm);
}

.content-body {
    flex: 1;
    padding: var(--spacing-xl);
    background: hsl(var(--muted) / 0.3);
}

main {
    background: hsl(var(--card));
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow);
    border: 1px solid hsl(var(--border));
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

/* --- Typography --- */
h1, h2, h3, h4, h5, h6 {
    color: hsl(var(--foreground));
    margin-bottom: var(--spacing-lg);
    font-weight: 600;
    line-height: 1.2;
}

h1 {
    font-size: 2.5rem;
    font-weight: 700;
}

h2 {
    font-size: 2rem;
    border-bottom: 1px solid hsl(var(--border));
    padding-bottom: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

h3 {
    font-size: 1.5rem;
}

h4 {
    font-size: 1.25rem;
}

p {
    margin-bottom: var(--spacing-md);
    color: hsl(var(--muted-foreground));
}

/* --- Sidebar Navigation --- */
.sidebar-nav {
    padding: var(--spacing-md) 0;
}

.nav-section {
    margin-bottom: var(--spacing-lg);
}

.nav-section-title {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: hsl(var(--muted-foreground));
    padding: 0 var(--spacing-lg);
    margin-bottom: var(--spacing-sm);
    transition: opacity var(--transition-fast);
}

.sidebar.collapsed .nav-section-title {
    opacity: 0;
}

.nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin-bottom: var(--spacing-xs);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    color: hsl(var(--muted-foreground));
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9375rem;
    transition: all var(--transition-fast);
    border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
    margin-right: var(--spacing-md);
    position: relative;
}

.nav-link:hover {
    background: hsl(var(--muted));
    color: hsl(var(--foreground));
}

.nav-link.active {
    background: hsl(var(--primary) / 0.1);
    color: hsl(var(--primary));
    border-right: 3px solid hsl(var(--primary));
}

.nav-link.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: hsl(var(--primary));
}

.nav-icon {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-text {
    transition: opacity var(--transition-fast);
}

.sidebar.collapsed .nav-text {
    opacity: 0;
}

.sidebar.collapsed .nav-link {
    justify-content: center;
    padding: var(--spacing-md);
    margin-right: 0;
    border-radius: var(--radius);
}

nav ul li.active:hover a {
    color: hsl(var(--primary-foreground));
}

/* --- Modern Button System --- */
.button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 0.9375rem;
    font-weight: 500;
    line-height: 1;
    border-radius: var(--radius);
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    border: 1px solid transparent;
    position: relative;
    overflow: hidden;

    /* Default (Primary) */
    background: var(--gradient-primary);
    color: hsl(var(--primary-foreground));
    box-shadow: var(--shadow-sm);
}

.button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left var(--transition-slow);
}

.button:hover::before {
    left: 100%;
}

.button:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.button:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

.button:focus-visible {
    outline: none;
    box-shadow: 0 0 0 2px hsl(var(--ring));
}

/* Button Variants */
.button-secondary {
    background: hsl(var(--secondary));
    color: hsl(var(--secondary-foreground));
    border: 1px solid hsl(var(--border));
}

.button-outline {
    background: transparent;
    color: hsl(var(--foreground));
    border: 1px solid hsl(var(--border));
    box-shadow: none;
}

.button-outline:hover {
    background: hsl(var(--muted));
    border-color: hsl(var(--border));
}

.button-ghost {
    background: transparent;
    color: hsl(var(--foreground));
    border: none;
    box-shadow: none;
}

.button-ghost:hover {
    background: hsl(var(--muted));
}

.button-destructive {
    background: hsl(var(--destructive));
    color: hsl(var(--destructive-foreground));
}

.button-success {
    background: hsl(var(--success));
    color: hsl(var(--success-foreground));
}

.button-warning {
    background: hsl(var(--warning));
    color: hsl(var(--warning-foreground));
}

/* Button Sizes */
.button-sm {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.875rem;
}

.button-lg {
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: 1.125rem;
}

.button-icon {
    padding: var(--spacing-md);
    width: 40px;
    height: 40px;
}

/* --- Modern Form System --- */
.form-group {
    margin-bottom: var(--spacing-lg);
    position: relative;
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: hsl(var(--foreground));
    font-size: 0.9375rem;
    transition: color var(--transition-fast);
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-md);
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
    font-size: 1rem;
    color: hsl(var(--foreground));
    background-color: hsl(var(--background));
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    border-color: hsl(var(--primary));
    box-shadow: 0 0 0 3px hsl(var(--primary) / 0.1);
    outline: none;
}

.form-input:hover,
.form-select:hover,
.form-textarea:hover {
    border-color: hsl(var(--muted-foreground));
}

/* Legacy form support */
form div {
    margin-bottom: var(--spacing-lg);
}

form label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: hsl(var(--foreground));
    font-size: 0.9375rem;
}

form input[type="text"],
form input[type="email"],
form input[type="number"],
form input[type="password"],
form select,
form textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
    font-size: 1rem;
    color: hsl(var(--foreground));
    background-color: hsl(var(--background));
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

form input:focus,
form select:focus,
form textarea:focus {
    border-color: hsl(var(--primary));
    box-shadow: 0 0 0 3px hsl(var(--primary) / 0.1);
    outline: none;
}

form input:hover,
form select:hover,
form textarea:hover {
    border-color: hsl(var(--muted-foreground));
}

/* Input states */
.form-input.error,
form input.error {
    border-color: hsl(var(--destructive));
    box-shadow: 0 0 0 3px hsl(var(--destructive) / 0.1);
}

.form-input.success,
form input.success {
    border-color: hsl(var(--success));
    box-shadow: 0 0 0 3px hsl(var(--success) / 0.1);
}

.form-help-text {
    font-size: 0.875rem;
    color: hsl(var(--muted-foreground));
    margin-top: var(--spacing-xs);
}

.form-error-text {
    font-size: 0.875rem;
    color: hsl(var(--destructive));
    margin-top: var(--spacing-xs);
}

/* --- Modern Card System --- */
.card {
    background: hsl(var(--card));
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: all var(--transition-fast);
}

.card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid hsl(var(--border));
    background: hsl(var(--muted) / 0.3);
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: hsl(var(--foreground));
    margin: 0;
}

.card-description {
    font-size: 0.9375rem;
    color: hsl(var(--muted-foreground));
    margin: var(--spacing-xs) 0 0 0;
}

.card-content {
    padding: var(--spacing-lg);
}

.card-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid hsl(var(--border));
    background: hsl(var(--muted) / 0.3);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* --- Stats Cards --- */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.stat-card {
    background: hsl(var(--card));
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow);
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
}

.stat-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: hsl(var(--muted-foreground));
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.stat-icon {
    width: 24px;
    height: 24px;
    color: hsl(var(--primary));
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: hsl(var(--foreground));
    margin-bottom: var(--spacing-xs);
}

.stat-change {
    font-size: 0.875rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.stat-change.positive {
    color: hsl(var(--success));
}

.stat-change.negative {
    color: hsl(var(--destructive));
}

/* --- Utility Classes --- */
.flex {
    display: flex;
}

.flex-col {
    flex-direction: column;
}

.items-center {
    align-items: center;
}

.justify-between {
    justify-content: space-between;
}

.gap-2 {
    gap: var(--spacing-sm);
}

.gap-4 {
    gap: var(--spacing-md);
}

.mb-4 {
    margin-bottom: var(--spacing-md);
}

.mb-6 {
    margin-bottom: var(--spacing-lg);
}

.text-sm {
    font-size: 0.875rem;
}

.text-muted {
    color: hsl(var(--muted-foreground));
}

.font-medium {
    font-weight: 500;
}

.font-semibold {
    font-weight: 600;
}

/* --- Dark Mode Toggle --- */
.theme-toggle {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    z-index: 100;
    background: hsl(var(--card));
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
    padding: var(--spacing-md);
    cursor: pointer;
    box-shadow: var(--shadow);
    transition: all var(--transition-fast);
}

.theme-toggle:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

/* Specific form adjustments for order items */
.order-item {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr auto;
    gap: 1rem;
    align-items: flex-end;
    margin-bottom: 1rem;
    padding: 1rem;
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
    background-color: hsl(var(--muted) / 0.5);
}

.order-item .remove-item {
    background-color: hsl(var(--destructive));
    border-color: hsl(var(--destructive));
    color: hsl(var(--destructive-foreground));
    padding: 0.625rem 0.75rem;
    font-size: 0.875rem;
    height: fit-content;
}

.order-item .remove-item:hover {
    background-color: hsl(var(--destructive) / 0.9);
    border-color: hsl(var(--destructive) / 0.9);
}

/* --- Modern Data Tables --- */
.table-container {
    background: hsl(var(--card));
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow);
}

.table-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid hsl(var(--border));
    background: hsl(var(--muted) / 0.3);
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.table-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: hsl(var(--foreground));
    margin: 0;
}

.table-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.table-search {
    position: relative;
}

.table-search input {
    padding: var(--spacing-sm) var(--spacing-md);
    padding-left: 2.5rem;
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
    background: hsl(var(--background));
    color: hsl(var(--foreground));
    font-size: 0.9375rem;
    width: 250px;
    transition: all var(--transition-fast);
}

.table-search input:focus {
    border-color: hsl(var(--primary));
    box-shadow: 0 0 0 3px hsl(var(--primary) / 0.1);
    outline: none;
}

.table-search::before {
    content: '🔍';
    position: absolute;
    left: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: hsl(var(--muted-foreground));
}

.data-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.data-table th,
.data-table td {
    padding: var(--spacing-md) var(--spacing-lg);
    text-align: left;
    border-bottom: 1px solid hsl(var(--border));
}

.data-table th {
    background: hsl(var(--muted) / 0.5);
    font-weight: 600;
    color: hsl(var(--foreground));
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    position: sticky;
    top: 0;
    z-index: 10;
}

.data-table tbody tr {
    transition: all var(--transition-fast);
}

.data-table tbody tr:hover {
    background: hsl(var(--muted) / 0.3);
}

.data-table tbody tr:last-child td {
    border-bottom: none;
}

/* Table action buttons */
.table-actions-cell {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.action-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: var(--radius);
    text-decoration: none;
    transition: all var(--transition-fast);
    border: 1px solid transparent;
}

.action-button.edit {
    background: hsl(var(--primary) / 0.1);
    color: hsl(var(--primary));
    border-color: hsl(var(--primary) / 0.2);
}

.action-button.edit:hover {
    background: hsl(var(--primary) / 0.2);
}

.action-button.delete {
    background: hsl(var(--destructive) / 0.1);
    color: hsl(var(--destructive));
    border-color: hsl(var(--destructive) / 0.2);
}

.action-button.delete:hover {
    background: hsl(var(--destructive) / 0.2);
}

.action-button.view {
    background: hsl(var(--muted));
    color: hsl(var(--muted-foreground));
}

.action-button.view:hover {
    background: hsl(var(--muted) / 0.8);
    color: hsl(var(--foreground));
}

/* Table pagination */
.table-pagination {
    padding: var(--spacing-lg);
    border-top: 1px solid hsl(var(--border));
    background: hsl(var(--muted) / 0.3);
    display: flex;
    align-items: center;
    justify-content: between;
}

.pagination-info {
    color: hsl(var(--muted-foreground));
    font-size: 0.875rem;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-left: auto;
}

/* Legacy table support */
table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-top: var(--spacing-lg);
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius-lg);
    overflow: hidden;
    background: hsl(var(--card));
    box-shadow: var(--shadow);
}

table th,
table td {
    padding: var(--spacing-md) var(--spacing-lg);
    text-align: left;
    border-bottom: 1px solid hsl(var(--border));
}

table th {
    background: hsl(var(--muted) / 0.5);
    font-weight: 600;
    color: hsl(var(--foreground));
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

table tbody tr:last-child td {
    border-bottom: none;
}

table tbody tr:hover {
    background: hsl(var(--muted) / 0.3);
}

/* Table actions links */
table td a {
    margin-right: var(--spacing-md);
    font-size: 0.875rem;
    color: hsl(var(--primary));
    text-decoration: none;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius);
    transition: all var(--transition-fast);
}

table td a:hover {
    background: hsl(var(--primary) / 0.1);
    color: hsl(var(--primary));
}

/* Footer for tables (e.g., total) */
table tfoot td {
    font-weight: 600;
    background: hsl(var(--muted));
    border-top: 1px solid hsl(var(--border));
    color: hsl(var(--foreground));
}

/* --- Responsive Design --- */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }

    main {
        padding: 20px;
        margin: 10px auto;
    }

    nav ul {
        flex-direction: column;
        gap: 0.5rem;
        padding: 0 10px;
    }

    nav ul li a {
        display: block;
        text-align: center;
    }

    .order-item {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .order-item div {
        margin-bottom: 0;
    }

    table,
    thead,
    tbody,
    th,
    td,
    tr {
        display: block;
    }

    thead tr {
        position: absolute;
        top: -9999px;
        left: -9999px;
    }

    tr {
        border: 1px solid hsl(var(--border));
        margin-bottom: 10px;
        border-radius: var(--radius);
        overflow: hidden;
    }

    td {
        border: none;
        border-bottom: 1px solid hsl(var(--border));
        position: relative;
        padding-left: 50%;
        text-align: right;
    }

    td:last-child {
        border-bottom: none;
    }

    td:before {
        position: absolute;
        top: 0;
        left: 6px;
        width: 45%;
        padding-right: 10px;
        white-space: nowrap;
        text-align: left;
        font-weight: 600;
        color: hsl(var(--muted-foreground));
    }

    /* Label the data */
    td:nth-of-type(1):before { content: "ID:"; }
    td:nth-of-type(2):before { content: "Name:"; }
    td:nth-of-type(3):before { content: "Email:"; }
    td:nth-of-type(4):before { content: "Actions:"; }

    /* Specific for products table */
    .products-table td:nth-of-type(1):before { content: "ID:"; }
    .products-table td:nth-of-type(2):before { content: "Name:"; }
    .products-table td:nth-of-type(3):before { content: "SKU:"; }
    .products-table td:nth-of-type(4):before { content: "Quantity:"; }
    .products-table td:nth-of-type(5):before { content: "Price:"; }
    .products-table td:nth-of-type(6):before { content: "Actions:"; }

    /* Specific for sales table */
    .sales-table td:nth-of-type(1):before { content: "Order ID:"; }
    .sales-table td:nth-of-type(2):before { content: "Customer:"; }
    .sales-table td:nth-of-type(3):before { content: "Order Date:"; }
    .sales-table td:nth-of-type(4):before { content: "Actions:"; }

    /* Specific for order items table */
    .order-items-table td:nth-of-type(1):before { content: "Product Name:"; }
    .order-items-table td:nth-of-type(2):before { content: "SKU:"; }
    .order-items-table td:nth-of-type(3):before { content: "Quantity:"; }
    .order-items-table td:nth-of-type(4):before { content: "Price:"; }
    .order-items-table td:nth-of-type(5):before { content: "Total:"; }

    table tfoot td {
        text-align: right !important;
        padding-left: 6px;
    }
}
/* --- Login Page Styles --- */
.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: hsl(var(--muted)); /* Light background for the whole page */
    padding: 20px;
}

.login-card {
    background-color: hsl(var(--card));
    padding: 40px;
    border-radius: var(--radius);
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    width: 100%;
    max-width: 400px;
    text-align: center;
    border: 1px solid hsl(var(--border));
}

.login-card h2 {
    font-size: 2rem;
    color: hsl(var(--foreground));
    margin-bottom: 1.5rem;
    border-bottom: none; /* Remove border from h2 in login card */
    padding-bottom: 0;
}

.login-card .form-group {
    margin-bottom: 1.25rem;
    text-align: left;
}

.login-card label {
    font-size: 0.9375rem;
    color: hsl(var(--muted-foreground));
    margin-bottom: 0.5rem;
}

.login-card input[type="email"],
.login-card input[type="password"] {
    width: 100%;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    border: 1px solid hsl(var(--input));
    border-radius: var(--radius);
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.login-card input[type="email"]:focus,
.login-card input[type="password"]:focus {
    border-color: hsl(var(--primary));
    box-shadow: 0 0 0 2px hsl(var(--ring));
    outline: none;
}

.login-card .login-button {
    width: 100%;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    font-weight: 600;
    background-color: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
    border: none;
    border-radius: var(--radius);
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.login-card .login-button:hover {
    background-color: hsl(var(--primary) / 0.9);
}

.login-card .error-message {
    color: hsl(var(--destructive));
    margin-bottom: 1rem;
    font-size: 0.875rem;
}

/* --- Interactive Elements & Animations --- */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes shimmer {
    0% { background-position: -200px 0; }
    100% { background-position: calc(200px + 100%) 0; }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.animate-fade-in {
    animation: fadeIn 0.5s ease-out;
}

.animate-slide-in {
    animation: slideIn 0.3s ease-out;
}

.loading-skeleton {
    background: linear-gradient(90deg, hsl(var(--muted)) 25%, hsl(var(--muted) / 0.5) 50%, hsl(var(--muted)) 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* Hover effects */
.hover-lift {
    transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Focus states */
.focus-ring:focus-visible {
    outline: none;
    box-shadow: 0 0 0 3px hsl(var(--ring) / 0.3);
}

/* Loading states */
.loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid hsl(var(--muted));
    border-top: 2px solid hsl(var(--primary));
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Notification styles */
.notification {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    background: hsl(var(--card));
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-xl);
    z-index: 1000;
    animation: slideIn 0.3s ease-out;
    max-width: 400px;
}

.notification.success {
    border-left: 4px solid hsl(var(--success));
}

.notification.error {
    border-left: 4px solid hsl(var(--destructive));
}

.notification.warning {
    border-left: 4px solid hsl(var(--warning));
}

/* Enhanced mobile responsiveness */
@media (max-width: 1024px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform var(--transition-normal);
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .table-header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-md);
    }

    .table-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .table-search input {
        width: 100%;
    }
}

@media (max-width: 768px) {
    .content-header {
        padding: var(--spacing-md);
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }

    .content-body {
        padding: var(--spacing-md);
    }

    main {
        padding: var(--spacing-md);
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .card-footer {
        flex-direction: column;
        align-items: stretch;
    }

    .card-footer .flex {
        flex-direction: column;
    }

    .data-table {
        font-size: 0.875rem;
    }

    .data-table th,
    .data-table td {
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .table-actions-cell {
        flex-direction: column;
        align-items: stretch;
    }

    .action-button {
        justify-content: center;
        width: 100%;
    }
}

/* public/assets/style.css */

/* --- CSS Variables (Shadcn-like Palette) --- */
:root {
    --background: 0 0% 100%; /* hsl(0 0% 100%) */
    --foreground: 222.2 84% 4.9%; /* hsl(222.2 84% 4.9%) */

    --card: 0 0% 100%; /* hsl(0 0% 100%) */
    --card-foreground: 222.2 84% 4.9%; /* hsl(222.2 84% 4.9%) */

    --popover: 0 0% 100%; /* hsl(0 0% 100%) */
    --popover-foreground: 222.2 84% 4.9%; /* hsl(222.2 84% 4.9%) */

    --primary: 222.2 47.4% 11.2%; /* hsl(222.2 47.4% 11.2%) */
    --primary-foreground: 210 40% 98%; /* hsl(210 40% 98%) */

    --secondary: 210 40% 96.1%; /* hsl(210 40% 96.1%) */
    --secondary-foreground: 222.2 47.4% 11.2%; /* hsl(222.2 47.4% 11.2%) */

    --muted: 210 40% 96.1%; /* hsl(210 40% 96.1%) */
    --muted-foreground: 215.4 16.3% 46.9%; /* hsl(215.4 16.3% 46.9%) */

    --accent: 210 40% 96.1%; /* hsl(210 40% 96.1%) */
    --accent-foreground: 222.2 47.4% 11.2%; /* hsl(222.2 47.4% 11.2%) */

    --destructive: 0 84.2% 60.2%; /* hsl(0 84.2% 60.2%) */
    --destructive-foreground: 210 40% 98%; /* hsl(210 40% 98%) */

    --border: 214.3 31.8% 91.4%; /* hsl(214.3 31.8% 91.4%) */
    --input: 214.3 31.8% 91.4%; /* hsl(214.3 31.8% 91.4%) */
    --ring: 222.2 84% 4.9%; /* hsl(222.2 84% 4.9%) */

    --radius: 0.5rem;
}

/* Helper to convert HSL variables to actual HSL values */
@function hsl-var($h, $s, $l) {
    @return hsl(var($h) var($s) var($l));
}

/* --- Base Styles & Reset --- */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    line-height: 1.5;
    color: hsl(var(--foreground));
    background-color: hsl(var(--background));
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

main {
    flex-grow: 1;
    max-width: 1200px;
    width: 100%;
    margin: 20px auto;
    background-color: hsl(var(--card));
    padding: 30px;
    border-radius: var(--radius);
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    border: 1px solid hsl(var(--border));
}

/* --- Typography --- */
h1, h2, h3, h4, h5, h6 {
    color: hsl(var(--foreground));
    margin-bottom: 1.25rem;
    font-weight: 600;
}

h2 {
    font-size: 1.75rem;
    border-bottom: 1px solid hsl(var(--border));
    padding-bottom: 0.75rem;
    margin-bottom: 1.5rem;
}

p {
    margin-bottom: 1rem;
}

/* --- Navigation --- */
nav {
    background-color: hsl(var(--card));
    padding: 1rem 0;
    border-radius: var(--radius);
    margin: 20px auto 0 auto; /* Center nav with main content */
    max-width: 1200px;
    width: 100%;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.05), 0 1px 2px -1px rgb(0 0 0 / 0.05);
    border: 1px solid hsl(var(--border));
}

nav ul {
    list-style: none;
    display: flex;
    justify-content: center;
    gap: 2rem;
}

nav ul li {
    padding: 0.75rem 1rem;
    border-radius: calc(var(--radius) - 2px);
    transition: background-color 0.2s ease, color 0.2s ease;
}

nav ul li a {
    color: hsl(var(--muted-foreground));
    text-decoration: none;
    font-weight: 500;
}

nav ul li:hover {
    background-color: hsl(var(--accent));
}

nav ul li:hover a {
    color: hsl(var(--accent-foreground));
}

nav ul li.active {
    background-color: hsl(var(--primary));
}

nav ul li.active a {
    color: hsl(var(--primary-foreground));
}

nav ul li.active:hover {
    background-color: hsl(var(--primary) / 0.9);
}

nav ul li.active:hover a {
    color: hsl(var(--primary-foreground));
}

/* --- Buttons --- */
.button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.625rem 1.25rem; /* 10px 20px */
    font-size: 0.9375rem; /* 15px */
    font-weight: 500;
    line-height: 1;
    border-radius: var(--radius);
    cursor: pointer;
    transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease, box-shadow 0.2s ease;
    text-decoration: none; /* Ensure no underline */

    /* Default (Primary) */
    background-color: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
    border: 1px solid hsl(var(--primary));
}

.button:hover {
    background-color: hsl(var(--primary) / 0.9);
    border-color: hsl(var(--primary) / 0.9);
}

.button:focus-visible {
    outline: none;
    box-shadow: 0 0 0 2px hsl(var(--ring));
}

/* Button Variants */
.button-secondary {
    background-color: hsl(var(--secondary));
    color: hsl(var(--secondary-foreground));
    border-color: hsl(var(--secondary));
}

.button-secondary:hover {
    background-color: hsl(var(--secondary) / 0.8);
    border-color: hsl(var(--secondary) / 0.8);
}

.button-outline {
    background-color: transparent;
    color: hsl(var(--foreground));
    border: 1px solid hsl(var(--input));
}

.button-outline:hover {
    background-color: hsl(var(--accent));
    color: hsl(var(--accent-foreground));
}

.button-destructive {
    background-color: hsl(var(--destructive));
    color: hsl(var(--destructive-foreground));
    border-color: hsl(var(--destructive));
}

.button-destructive:hover {
    background-color: hsl(var(--destructive) / 0.9);
    border-color: hsl(var(--destructive) / 0.9);
}

/* --- Forms --- */
form div {
    margin-bottom: 1rem;
}

form label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: hsl(var(--foreground));
    font-size: 0.9375rem;
}

form input[type="text"],
form input[type="email"],
form input[type="number"],
form input[type="password"],
form select,
form textarea {
    width: 100%;
    padding: 0.625rem 0.75rem; /* 10px 12px */
    border: 1px solid hsl(var(--input));
    border-radius: calc(var(--radius) - 2px);
    font-size: 1rem;
    color: hsl(var(--foreground));
    background-color: hsl(var(--background));
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

form input:focus,
form select:focus,
form textarea:focus {
    border-color: hsl(var(--ring));
    box-shadow: 0 0 0 2px hsl(var(--ring));
    outline: none;
}

/* Specific form adjustments for order items */
.order-item {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr auto;
    gap: 1rem;
    align-items: flex-end;
    margin-bottom: 1rem;
    padding: 1rem;
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
    background-color: hsl(var(--muted) / 0.5);
}

.order-item .remove-item {
    background-color: hsl(var(--destructive));
    border-color: hsl(var(--destructive));
    color: hsl(var(--destructive-foreground));
    padding: 0.625rem 0.75rem;
    font-size: 0.875rem;
    height: fit-content;
}

.order-item .remove-item:hover {
    background-color: hsl(var(--destructive) / 0.9);
    border-color: hsl(var(--destructive) / 0.9);
}

/* --- Tables --- */
table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-top: 1.5rem;
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
    overflow: hidden;
}

table th,
table td {
    padding: 0.75rem 1rem;
    text-align: left;
    border-bottom: 1px solid hsl(var(--border));
}

table th {
    background-color: hsl(var(--muted));
    font-weight: 600;
    color: hsl(var(--muted-foreground));
    font-size: 0.9375rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

table tbody tr:last-child td {
    border-bottom: none;
}

table tbody tr:hover {
    background-color: hsl(var(--muted) / 0.5);
}

/* Table actions links */
table td a {
    margin-right: 0.75rem;
    font-size: 0.9375rem;
    color: hsl(var(--primary));
}

table td a:hover {
    text-decoration: underline;
}

/* Footer for tables (e.g., total) */
table tfoot td {
    font-weight: 600;
    background-color: hsl(var(--muted));
    border-top: 1px solid hsl(var(--border));
    color: hsl(var(--foreground));
}

/* --- Responsive Design --- */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }

    main {
        padding: 20px;
        margin: 10px auto;
    }

    nav ul {
        flex-direction: column;
        gap: 0.5rem;
        padding: 0 10px;
    }

    nav ul li a {
        display: block;
        text-align: center;
    }

    .order-item {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .order-item div {
        margin-bottom: 0;
    }

    table,
    thead,
    tbody,
    th,
    td,
    tr {
        display: block;
    }

    thead tr {
        position: absolute;
        top: -9999px;
        left: -9999px;
    }

    tr {
        border: 1px solid hsl(var(--border));
        margin-bottom: 10px;
        border-radius: var(--radius);
        overflow: hidden;
    }

    td {
        border: none;
        border-bottom: 1px solid hsl(var(--border));
        position: relative;
        padding-left: 50%;
        text-align: right;
    }

    td:last-child {
        border-bottom: none;
    }

    td:before {
        position: absolute;
        top: 0;
        left: 6px;
        width: 45%;
        padding-right: 10px;
        white-space: nowrap;
        text-align: left;
        font-weight: 600;
        color: hsl(var(--muted-foreground));
    }

    /* Label the data */
    td:nth-of-type(1):before { content: "ID:"; }
    td:nth-of-type(2):before { content: "Name:"; }
    td:nth-of-type(3):before { content: "Email:"; }
    td:nth-of-type(4):before { content: "Actions:"; }

    /* Specific for products table */
    .products-table td:nth-of-type(1):before { content: "ID:"; }
    .products-table td:nth-of-type(2):before { content: "Name:"; }
    .products-table td:nth-of-type(3):before { content: "SKU:"; }
    .products-table td:nth-of-type(4):before { content: "Quantity:"; }
    .products-table td:nth-of-type(5):before { content: "Price:"; }
    .products-table td:nth-of-type(6):before { content: "Actions:"; }

    /* Specific for sales table */
    .sales-table td:nth-of-type(1):before { content: "Order ID:"; }
    .sales-table td:nth-of-type(2):before { content: "Customer:"; }
    .sales-table td:nth-of-type(3):before { content: "Order Date:"; }
    .sales-table td:nth-of-type(4):before { content: "Actions:"; }

    /* Specific for order items table */
    .order-items-table td:nth-of-type(1):before { content: "Product Name:"; }
    .order-items-table td:nth-of-type(2):before { content: "SKU:"; }
    .order-items-table td:nth-of-type(3):before { content: "Quantity:"; }
    .order-items-table td:nth-of-type(4):before { content: "Price:"; }
    .order-items-table td:nth-of-type(5):before { content: "Total:"; }

    table tfoot td {
        text-align: right !important;
        padding-left: 6px;
    }
}
/* --- Login Page Styles --- */
.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: hsl(var(--muted)); /* Light background for the whole page */
    padding: 20px;
}

.login-card {
    background-color: hsl(var(--card));
    padding: 40px;
    border-radius: var(--radius);
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    width: 100%;
    max-width: 400px;
    text-align: center;
    border: 1px solid hsl(var(--border));
}

.login-card h2 {
    font-size: 2rem;
    color: hsl(var(--foreground));
    margin-bottom: 1.5rem;
    border-bottom: none; /* Remove border from h2 in login card */
    padding-bottom: 0;
}

.login-card .form-group {
    margin-bottom: 1.25rem;
    text-align: left;
}

.login-card label {
    font-size: 0.9375rem;
    color: hsl(var(--muted-foreground));
    margin-bottom: 0.5rem;
}

.login-card input[type="email"],
.login-card input[type="password"] {
    width: 100%;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    border: 1px solid hsl(var(--input));
    border-radius: var(--radius);
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.login-card input[type="email"]:focus,
.login-card input[type="password"]:focus {
    border-color: hsl(var(--primary));
    box-shadow: 0 0 0 2px hsl(var(--ring));
    outline: none;
}

.login-card .login-button {
    width: 100%;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    font-weight: 600;
    background-color: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
    border: none;
    border-radius: var(--radius);
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.login-card .login-button:hover {
    background-color: hsl(var(--primary) / 0.9);
}

.login-card .error-message {
    color: hsl(var(--destructive));
    margin-bottom: 1rem;
    font-size: 0.875rem;
}

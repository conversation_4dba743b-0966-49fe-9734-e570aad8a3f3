<?php

// modules/users/actions.php

function list_action() {
    $db = db_connect();
    $stmt = $db->query("SELECT * FROM users");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    render('modules/users/views/list', ['users' => $users]);
}

function new_action() {
    render('modules/users/views/form');
}

function save_action() {
    $db = db_connect();
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $name = $_POST['name'];
        $email = $_POST['email'];
        $password = $_POST['password'] ?? ''; // Get password, default to empty string if not set

        if (isset($_POST['id']) && !empty($_POST['id'])) {
            // Update existing user
            $sql = "UPDATE users SET name = :name, email = :email";
            $params = [
                ':name' => $name,
                ':email' => $email,
                ':id' => $_POST['id'],
            ];
            if (!empty($password)) {
                $sql .= ", password = :password";
                $params[':password'] = password_hash($password, PASSWORD_DEFAULT);
            }
            $sql .= " WHERE id = :id";
            $stmt = $db->prepare($sql);
            $stmt->execute($params);
        } else {
            // Create new user
            // Password is required for new users
            if (empty($password)) {
                // Handle error: password is required
                // For now, redirect back or show an error.
                // A more robust solution would involve client-side validation or a proper error message.
                header('Location: index.php?module=users&action=new&error=password_required');
                exit();
            }
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            $stmt = $db->prepare("INSERT INTO users (name, email, password) VALUES (:name, :email, :password)");
            $stmt->execute([
                ':name' => $name,
                ':email' => $email,
                ':password' => $hashed_password,
            ]);
        }
    }
    header('Location: index.php?module=users&action=list');
}

function edit_action() {
    $db = db_connect();
    $stmt = $db->prepare("SELECT * FROM users WHERE id = :id");
    $stmt->execute([':id' => $_GET['id']]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    render('modules/users/views/form', ['user' => $user]);
}

function delete_action() {
    $db = db_connect();
    $stmt = $db->prepare("DELETE FROM users WHERE id = :id");
    $stmt->execute([':id' => $_GET['id']]);
    header('Location: index.php?module=users&action=list&deleted=1');
}

function bulk_delete_action() {
    $db = db_connect();
    $user_ids = $_POST['user_ids'] ?? [];

    if (empty($user_ids)) {
        header('Location: index.php?module=users&action=list&error=1');
        exit;
    }

    try {
        $placeholders = str_repeat('?,', count($user_ids) - 1) . '?';
        $stmt = $db->prepare("DELETE FROM users WHERE id IN ($placeholders)");
        $stmt->execute($user_ids);

        header('Location: index.php?module=users&action=list&deleted=' . count($user_ids));
        exit;
    } catch (PDOException $e) {
        error_log("Database error in bulk_delete_action: " . $e->getMessage());
        header('Location: index.php?module=users&action=list&error=1');
        exit;
    }
}

function bulk_update_action() {
    $db = db_connect();
    $user_ids = $_POST['user_ids'] ?? [];
    $status = $_POST['status'] ?? '';

    if (empty($user_ids) || empty($status)) {
        header('Location: index.php?module=users&action=list&error=1');
        exit;
    }

    try {
        // Note: Users table might not have status column, so this is a placeholder
        // You might need to add a status column or modify this logic
        header('Location: index.php?module=users&action=list&updated=' . count($user_ids));
        exit;
    } catch (PDOException $e) {
        error_log("Database error in bulk_update_action: " . $e->getMessage());
        header('Location: index.php?module=users&action=list&error=1');
        exit;
    }
}

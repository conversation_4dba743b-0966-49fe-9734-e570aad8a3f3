<?php

require_once 'erp-project/core/functions.php';

$db_path = 'erp-project/db/erp.sqlite';
$schema_path = 'erp-project/db/schema.sql';

// Connect to the database (this will create the file if it doesn't exist)
$pdo = new PDO("sqlite:{$db_path}");
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

// Read and execute schema.sql
$schema_sql = file_get_contents($schema_path);
$pdo->exec($schema_sql);

// Insert super admin user
$name = 'Super Admin';
$email = '<EMAIL>';
$password = '$2y$12$9eZQf.Yaobko9NnAO5f6IOKXU.OnRZxH728Ou1lsVduGP9pSeS3vC'; // Hashed password for 'adminpassword'

$stmt = $pdo->prepare("INSERT INTO users (name, email, password) VALUES (:name, :email, :password)");
$stmt->execute([
    ':name' => $name,
    ':email' => $email,
    ':password' => $password,
]);

echo "Database initialized and super admin user created successfully!";

?>
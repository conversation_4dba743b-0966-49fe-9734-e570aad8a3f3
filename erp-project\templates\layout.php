<?php
    // Check if the current module is 'auth' and action is 'login'
    $is_login_page = (isset($_GET['module']) && $_GET['module'] === 'auth' && isset($_GET['action']) && $_GET['action'] === 'login');

    // For login page, use minimal layout with just CSS
    if ($is_login_page && (!isset($_SESSION['user_id']) || !$_SESSION['user_id'])):
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - ERP System</title>
    <link rel="stylesheet" href="assets/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <?php echo $content; ?>

    <script>
        // Initialize theme on login page
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'dark') {
                document.documentElement.setAttribute('data-theme', 'dark');
            }
        });
    </script>
</body>
</html>
<?php
    else:
        // For all other pages, use the full layout with navigation
        require_once __DIR__ . '/header.php';
?>

<div class="container">
    <?php echo $content; ?>
</div>

<?php
        require_once __DIR__ . '/footer.php';
    endif;
?>

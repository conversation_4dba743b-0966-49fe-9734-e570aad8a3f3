<?php
    // Check if the current module is 'auth' and action is 'login'
    $is_login_page = (isset($_GET['module']) && $_GET['module'] === 'auth' && isset($_GET['action']) && $_GET['action'] === 'login');

    // Only include header if not on login page or if user is logged in (for other auth actions like logout)
    if (!$is_login_page || (isset($_SESSION['user_id']) && $_SESSION['user_id'])): // Check for user_id in session
        require_once __DIR__ . '/header.php';
    endif;
?>

<div class="container">
    <?php echo $content; ?>
</div>

<?php require_once __DIR__ . '/footer.php'; ?>

<!-- Success/Error Messages -->
<?php if (isset($_GET['success'])): ?>
    <div class="alert alert-success">
        <i data-lucide="check-circle"></i>
        <span>Provider added successfully!</span>
    </div>
<?php endif; ?>

<?php if (isset($_GET['updated'])): ?>
    <div class="alert alert-success">
        <i data-lucide="check-circle"></i>
        <span>Provider updated successfully!</span>
    </div>
<?php endif; ?>

<?php if (isset($_GET['deleted'])): ?>
    <div class="alert alert-success">
        <i data-lucide="check-circle"></i>
        <span>Provider deleted successfully!</span>
    </div>
<?php endif; ?>

<?php if (isset($_GET['error'])): ?>
    <div class="alert alert-error">
        <i data-lucide="alert-circle"></i>
        <span>An error occurred. Please try again.</span>
    </div>
<?php endif; ?>

<!-- Providers Table -->
<div class="table-container">
    <div class="table-header">
        <div class="table-title">
            <h2>Providers</h2>
            <p>Manage your service providers</p>
        </div>
        <div class="table-actions">
            <div class="search-box">
                <i data-lucide="search"></i>
                <input type="text" placeholder="Search providers..." class="search-input">
            </div>
            <button class="button button-outline">
                <i data-lucide="filter"></i>
                <span>Filter</span>
            </button>
            <a href="index.php?module=contacts&action=new_provider" class="button">
                <i data-lucide="plus"></i>
                <span>Add Provider</span>
            </a>
        </div>
    </div>

    <div class="table-content">
        <?php if (empty($providers)): ?>
            <div style="text-align: center; padding: var(--spacing-2xl); color: hsl(var(--muted-foreground));">
                <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">
                    <i data-lucide="building" style="width: 48px; height: 48px; stroke-width: 1;"></i>
                </div>
                <h3 style="margin-bottom: var(--spacing-sm);">No providers found</h3>
                <p style="margin-bottom: var(--spacing-lg);">Start by adding your first provider</p>
                <a href="index.php?module=contacts&action=new_provider" class="button">
                    <i data-lucide="plus"></i>
                    <span>Add Provider</span>
                </a>
            </div>
        <?php else: ?>
            <table class="table">
                <thead>
                    <tr>
                        <th>Provider</th>
                        <th>Contact</th>
                        <th>Service Type</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($providers as $provider): ?>
                        <tr>
                            <td>
                                <div class="user-info">
                                    <div class="user-avatar">
                                        <?php echo strtoupper(substr($provider['name'], 0, 1)); ?>
                                    </div>
                                    <div class="user-details">
                                        <div class="user-name"><?php echo htmlspecialchars($provider['name']); ?></div>
                                        <div class="user-role"><?php echo htmlspecialchars($provider['contact_person'] ?? 'No contact person'); ?></div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div style="display: flex; flex-direction: column; gap: 2px;">
                                    <span style="font-weight: 500;"><?php echo htmlspecialchars($provider['email']); ?></span>
                                    <?php if (!empty($provider['phone'])): ?>
                                        <span style="font-size: 0.75rem; color: hsl(var(--muted-foreground));"><?php echo htmlspecialchars($provider['phone']); ?></span>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <?php if (!empty($provider['service_type'])): ?>
                                    <span class="status-badge" style="background: hsl(var(--primary) / 0.1); color: hsl(var(--primary)); border: 1px solid hsl(var(--primary) / 0.2);">
                                        <?php echo ucfirst(str_replace('_', ' ', $provider['service_type'])); ?>
                                    </span>
                                <?php else: ?>
                                    <span style="color: hsl(var(--muted-foreground)); font-size: 0.875rem;">—</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="status-badge <?php echo strtolower($provider['status']); ?>">
                                    <?php echo $provider['status']; ?>
                                </span>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <a href="index.php?module=contacts&action=edit_provider&id=<?php echo $provider['id']; ?>"
                                       class="button button-sm button-outline" title="Edit">
                                        <i data-lucide="edit"></i>
                                    </a>
                                    <a href="index.php?module=contacts&action=delete_provider&id=<?php echo $provider['id']; ?>"
                                       class="button button-sm button-outline button-destructive"
                                       title="Delete"
                                       onclick="return confirm('Are you sure you want to delete this provider?')">
                                        <i data-lucide="trash-2"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>

    <?php if (!empty($providers)): ?>
        <div class="table-footer" style="padding: var(--spacing-md) var(--spacing-lg); border-top: 1px solid hsl(var(--border) / 0.2); background: hsl(var(--muted) / 0.1); display: flex; align-items: center; justify-content: space-between;">
            <div style="font-size: 0.875rem; color: hsl(var(--muted-foreground));">
                Showing <?php echo count($providers); ?> of <?php echo count($providers); ?> providers
            </div>
            <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                <button class="button button-outline button-sm" disabled>
                    <i data-lucide="chevron-left"></i>
                    <span>Previous</span>
                </button>
                <button class="button button-outline button-sm" disabled>
                    <span>Next</span>
                    <i data-lucide="chevron-right"></i>
                </button>
            </div>
        </div>
    <?php endif; ?>
</div>

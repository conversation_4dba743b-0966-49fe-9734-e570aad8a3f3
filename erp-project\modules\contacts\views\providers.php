<!-- Providers Management -->
<div class="table-container">
    <div class="table-header">
        <h2 class="table-title">Providers</h2>
        <div class="table-actions">
            <a href="index.php?module=contacts&action=new_provider" class="button">
                <i data-lucide="truck"></i>
                <span>Add Provider</span>
            </a>
        </div>
    </div>
</div>

<!-- Success/Error Messages -->
<?php if (isset($_GET['success'])): ?>
    <div class="alert alert-success">
        <i data-lucide="check-circle"></i>
        <span>Provider added successfully!</span>
    </div>
<?php endif; ?>

<?php if (isset($_GET['updated'])): ?>
    <div class="alert alert-success">
        <i data-lucide="check-circle"></i>
        <span>Provider updated successfully!</span>
    </div>
<?php endif; ?>

<?php if (isset($_GET['deleted'])): ?>
    <div class="alert alert-success">
        <i data-lucide="check-circle"></i>
        <span>Provider deleted successfully!</span>
    </div>
<?php endif; ?>

<!-- Providers Table -->
<div class="card">
    <div class="card-header">
        <div class="table-header">
            <div class="table-title">
                <h3>Providers</h3>
                <p>Manage your supplier relationships</p>
            </div>
            <div class="table-actions">
                <div class="search-box">
                    <i data-lucide="search"></i>
                    <input type="text" placeholder="Search providers..." class="search-input">
                </div>
                <button class="button button-outline">
                    <i data-lucide="filter"></i>
                    <span>Filter</span>
                </button>
            </div>
        </div>
    </div>
    
    <div class="card-content">
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Phone</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($providers as $provider): ?>
                        <tr>
                            <td>
                                <div class="user-info">
                                    <div class="user-avatar">
                                        <?php echo substr($provider['name'], 0, 1); ?>
                                    </div>
                                    <div class="user-details">
                                        <div class="user-name"><?php echo htmlspecialchars($provider['name']); ?></div>
                                        <div class="user-role">Provider</div>
                                    </div>
                                </div>
                            </td>
                            <td><?php echo htmlspecialchars($provider['email']); ?></td>
                            <td><?php echo htmlspecialchars($provider['phone']); ?></td>
                            <td>
                                <span class="status-badge <?php echo strtolower($provider['status']); ?>">
                                    <?php echo $provider['status']; ?>
                                </span>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <a href="index.php?module=contacts&action=edit_provider&id=<?php echo $provider['id']; ?>" 
                                       class="button button-sm button-outline" title="Edit">
                                        <i data-lucide="edit"></i>
                                    </a>
                                    <a href="index.php?module=contacts&action=delete_provider&id=<?php echo $provider['id']; ?>" 
                                       class="button button-sm button-outline button-destructive" 
                                       title="Delete"
                                       onclick="return confirm('Are you sure you want to delete this provider?')">
                                        <i data-lucide="trash-2"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
    
    <div class="card-footer">
        <div class="table-info">
            <span>Showing <?php echo count($providers); ?> of <?php echo count($providers); ?> providers</span>
        </div>
        <div class="pagination">
            <button class="button button-outline button-sm" disabled>
                <i data-lucide="chevron-left"></i>
                <span>Previous</span>
            </button>
            <button class="button button-outline button-sm" disabled>
                <span>Next</span>
                <i data-lucide="chevron-right"></i>
            </button>
        </div>
    </div>
</div>

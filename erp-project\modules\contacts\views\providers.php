<!-- Success/Error Messages -->
<?php if (isset($_GET['success'])): ?>
    <div class="alert alert-success">
        <i data-lucide="check-circle"></i>
        <span>Provider added successfully!</span>
    </div>
<?php endif; ?>

<?php if (isset($_GET['updated'])): ?>
    <div class="alert alert-success">
        <i data-lucide="check-circle"></i>
        <span>
            <?php
            $count = intval($_GET['updated']);
            echo $count > 1 ? "$count providers updated successfully!" : "Provider updated successfully!";
            ?>
        </span>
    </div>
<?php endif; ?>

<?php if (isset($_GET['deleted'])): ?>
    <div class="alert alert-success">
        <i data-lucide="check-circle"></i>
        <span>
            <?php
            $count = intval($_GET['deleted']);
            echo $count > 1 ? "$count providers deleted successfully!" : "Provider deleted successfully!";
            ?>
        </span>
    </div>
<?php endif; ?>

<?php if (isset($_GET['error'])): ?>
    <div class="alert alert-error">
        <i data-lucide="alert-circle"></i>
        <span>An error occurred. Please try again.</span>
    </div>
<?php endif; ?>

<!-- Providers Table -->
<div class="table-container">
    <div class="table-header">
        <div class="table-title">
            <h2>Providers</h2>
            <p>Manage your service providers</p>
        </div>
        <div class="table-actions">
            <div id="bulkActionsProviders" style="display: none; margin-right: 8px; gap: 6px;">
                <button class="bulk-action-btn bulk-delete" onclick="bulkDeleteProviders()" title="Delete selected providers">
                    <i data-lucide="trash-2"></i>
                    <span>Delete</span>
                </button>
                <button class="bulk-action-btn bulk-activate" onclick="bulkActivateProviders()" title="Activate selected providers">
                    <i data-lucide="check-circle"></i>
                    <span>Activate</span>
                </button>
                <button class="bulk-action-btn bulk-deactivate" onclick="bulkDeactivateProviders()" title="Deactivate selected providers">
                    <i data-lucide="pause-circle"></i>
                    <span>Deactivate</span>
                </button>
            </div>
            <div class="search-box">
                <i data-lucide="search"></i>
                <input type="text" id="providerSearch" placeholder="Search providers..." class="search-input">
            </div>
            <div class="filter-dropdown">
                <button class="button button-outline" id="filterBtnProviders" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px); border: 1px solid hsl(var(--border) / 0.3); box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    <i data-lucide="sliders-horizontal"></i>
                    <span>Filter</span>
                </button>
                <div class="filter-dropdown-content" id="filterDropdownProviders">
                    <div class="filter-option active" data-filter="all">All Providers</div>
                    <div class="filter-option" data-filter="active">Active Only</div>
                    <div class="filter-option" data-filter="inactive">Inactive Only</div>
                    <div class="filter-option" data-filter="supplies">Office Supplies</div>
                    <div class="filter-option" data-filter="technology">Technology</div>
                    <div class="filter-option" data-filter="logistics">Logistics</div>
                    <div class="filter-option" data-filter="consulting">Consulting</div>
                </div>
            </div>
            <a href="index.php?module=contacts&action=new_provider" class="button" style="background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary) / 0.8) 100%); color: white; box-shadow: 0 2px 8px hsl(var(--primary) / 0.3);">
                <i data-lucide="building-2"></i>
                <span>Add Provider</span>
            </a>
        </div>
    </div>

    <div class="table-content">
        <?php if (empty($providers)): ?>
            <div style="text-align: center; padding: var(--spacing-2xl); color: hsl(var(--muted-foreground));">
                <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">
                    <i data-lucide="building" style="width: 48px; height: 48px; stroke-width: 1;"></i>
                </div>
                <h3 style="margin-bottom: var(--spacing-sm);">No providers found</h3>
                <p style="margin-bottom: var(--spacing-lg);">Start by adding your first provider</p>
                <a href="index.php?module=contacts&action=new_provider" class="button">
                    <i data-lucide="plus"></i>
                    <span>Add Provider</span>
                </a>
            </div>
        <?php else: ?>
            <table class="table" id="providersTable">
                <thead>
                    <tr>
                        <th style="width: 3%;">
                            <input type="checkbox" id="selectAllProviders" style="margin: 0;">
                        </th>
                        <th style="width: 32%; cursor: pointer; user-select: none;" onclick="sortProvidersTable('name')" onmouseover="this.style.background='hsl(var(--muted) / 0.1)'" onmouseout="this.style.background=''">
                            Provider <i data-lucide="arrow-up-down" style="width: 12px; height: 12px; display: inline; opacity: 0.6; transition: opacity 0.2s;"></i>
                        </th>
                        <th style="width: 25%;">Contact</th>
                        <th style="width: 15%; cursor: pointer; user-select: none;" onclick="sortProvidersTable('service_type')" onmouseover="this.style.background='hsl(var(--muted) / 0.1)'" onmouseout="this.style.background=''">
                            Service Type <i data-lucide="arrow-up-down" style="width: 12px; height: 12px; display: inline; opacity: 0.6; transition: opacity 0.2s;"></i>
                        </th>
                        <th style="width: 10%; cursor: pointer; user-select: none;" onclick="sortProvidersTable('status')" onmouseover="this.style.background='hsl(var(--muted) / 0.1)'" onmouseout="this.style.background=''">
                            Status <i data-lucide="arrow-up-down" style="width: 12px; height: 12px; display: inline; opacity: 0.6; transition: opacity 0.2s;"></i>
                        </th>
                        <th style="width: 15%;">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($providers as $provider): ?>
                        <tr class="provider-row"
                            data-name="<?php echo strtolower(htmlspecialchars($provider['name'])); ?>"
                            data-email="<?php echo strtolower(htmlspecialchars($provider['email'])); ?>"
                            data-service-type="<?php echo strtolower(htmlspecialchars($provider['service_type'] ?? '')); ?>"
                            data-status="<?php echo strtolower($provider['status']); ?>"
                            data-id="<?php echo $provider['id']; ?>">
                            <td>
                                <input type="checkbox" class="row-checkbox-provider" value="<?php echo $provider['id']; ?>" style="margin: 0;">
                            </td>
                            <td>
                                <div class="user-info">
                                    <div class="user-avatar">
                                        <?php echo strtoupper(substr($provider['name'], 0, 1)); ?>
                                    </div>
                                    <div class="user-details">
                                        <div class="user-name"><?php echo htmlspecialchars($provider['name']); ?></div>
                                        <div class="user-role"><?php echo htmlspecialchars($provider['contact_person'] ?? 'No contact'); ?></div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div style="display: flex; flex-direction: column; gap: 1px;">
                                    <span style="font-weight: 500; font-size: 0.8rem;"><?php echo htmlspecialchars($provider['email']); ?></span>
                                    <?php if (!empty($provider['phone'])): ?>
                                        <span style="font-size: 0.7rem; color: hsl(var(--muted-foreground));"><?php echo htmlspecialchars($provider['phone']); ?></span>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <?php if (!empty($provider['service_type'])): ?>
                                    <span class="status-badge" style="background: hsl(var(--primary) / 0.1); color: hsl(var(--primary)); border: 1px solid hsl(var(--primary) / 0.2);">
                                        <?php echo ucfirst(str_replace('_', ' ', $provider['service_type'])); ?>
                                    </span>
                                <?php else: ?>
                                    <span style="color: hsl(var(--muted-foreground)); font-size: 0.8rem;">—</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="status-badge <?php echo strtolower($provider['status']); ?>">
                                    <?php echo $provider['status']; ?>
                                </span>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <a href="index.php?module=contacts&action=edit_provider&id=<?php echo $provider['id']; ?>"
                                       class="button button-sm button-edit" title="Edit Provider">
                                        <i data-lucide="edit-3"></i>
                                    </a>
                                    <button onclick="viewProvider(<?php echo $provider['id']; ?>)"
                                            class="button button-sm button-view"
                                            title="View Provider Details">
                                        <i data-lucide="eye"></i>
                                    </button>
                                    <button onclick="deleteProvider(<?php echo $provider['id']; ?>, '<?php echo htmlspecialchars($provider['name']); ?>')"
                                            class="button button-sm button-destructive"
                                            title="Delete Provider">
                                        <i data-lucide="trash-2"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>

    <?php if (!empty($providers)): ?>
        <div class="table-footer" style="padding: 8px 12px; border-top: 1px solid hsl(var(--border) / 0.15); background: hsl(var(--muted) / 0.05); display: flex; align-items: center; justify-content: space-between;">
            <div style="font-size: 0.8rem; color: hsl(var(--muted-foreground));">
                <span id="providerCount">Showing <?php echo count($providers); ?> of <?php echo count($providers); ?> providers</span>
            </div>
            <div style="display: flex; align-items: center; gap: 4px;">
                <button class="button button-outline button-sm" disabled>
                    <i data-lucide="chevron-left"></i>
                    <span>Previous</span>
                </button>
                <button class="button button-outline button-sm" disabled>
                    <span>Next</span>
                    <i data-lucide="chevron-right"></i>
                </button>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Floating Action Button -->
<button class="fab" onclick="window.location.href='index.php?module=contacts&action=new_provider'" title="Add New Provider">
    <i data-lucide="plus"></i>
</button>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('providerSearch');
    const filterBtn = document.getElementById('filterBtnProviders');
    const filterDropdown = document.getElementById('filterDropdownProviders');
    const filterOptions = document.querySelectorAll('#filterDropdownProviders .filter-option');
    const providerRows = document.querySelectorAll('.provider-row');
    const providerCount = document.getElementById('providerCount');
    const selectAllCheckbox = document.getElementById('selectAllProviders');
    const rowCheckboxes = document.querySelectorAll('.row-checkbox-provider');
    const bulkActions = document.getElementById('bulkActionsProviders');
    const totalProviders = providerRows.length;

    let currentFilter = 'all';
    let sortDirection = {};

    // Search functionality
    searchInput.addEventListener('input', function() {
        filterProviders();
    });

    // Filter dropdown toggle
    filterBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        filterDropdown.classList.toggle('show');
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', function() {
        filterDropdown.classList.remove('show');
    });

    // Filter options
    filterOptions.forEach(option => {
        option.addEventListener('click', function() {
            filterOptions.forEach(opt => opt.classList.remove('active'));
            this.classList.add('active');
            currentFilter = this.dataset.filter;
            filterBtn.querySelector('span').textContent = this.textContent;
            filterDropdown.classList.remove('show');
            filterProviders();
        });
    });

    // Select all functionality
    selectAllCheckbox.addEventListener('change', function() {
        const isChecked = this.checked;
        rowCheckboxes.forEach(checkbox => {
            if (checkbox.closest('.provider-row').style.display !== 'none') {
                checkbox.checked = isChecked;
            }
        });
        updateBulkActions();
    });

    // Individual checkbox functionality
    rowCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateBulkActions();
            updateSelectAll();
        });
    });

    function updateBulkActions() {
        const checkedBoxes = Array.from(rowCheckboxes).filter(cb =>
            cb.checked && cb.closest('.provider-row').style.display !== 'none'
        );
        bulkActions.style.display = checkedBoxes.length > 0 ? 'flex' : 'none';
    }

    function updateSelectAll() {
        const visibleCheckboxes = Array.from(rowCheckboxes).filter(cb =>
            cb.closest('.provider-row').style.display !== 'none'
        );
        const checkedVisible = visibleCheckboxes.filter(cb => cb.checked);

        selectAllCheckbox.checked = visibleCheckboxes.length > 0 &&
                                   checkedVisible.length === visibleCheckboxes.length;
        selectAllCheckbox.indeterminate = checkedVisible.length > 0 &&
                                         checkedVisible.length < visibleCheckboxes.length;
    }

    function filterProviders() {
        const searchTerm = searchInput.value.toLowerCase();
        let visibleCount = 0;

        providerRows.forEach(row => {
            const name = row.dataset.name;
            const email = row.dataset.email;
            const serviceType = row.dataset.serviceType;
            const status = row.dataset.status;

            const searchMatch = name.includes(searchTerm) ||
                              email.includes(searchTerm) ||
                              serviceType.includes(searchTerm);

            let filterMatch = true;
            if (currentFilter !== 'all') {
                if (currentFilter === 'active' || currentFilter === 'inactive') {
                    filterMatch = status === currentFilter;
                } else {
                    filterMatch = serviceType === currentFilter;
                }
            }

            if (searchMatch && filterMatch) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
                row.querySelector('.row-checkbox-provider').checked = false;
            }
        });

        providerCount.textContent = `Showing ${visibleCount} of ${totalProviders} providers`;
        updateBulkActions();
        updateSelectAll();
    }
});

function sortProvidersTable(column) {
    const table = document.getElementById('providersTable');
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('.provider-row'));

    // Toggle sort direction
    let sortDirection = {};
    sortDirection[column] = sortDirection[column] === 'asc' ? 'desc' : 'asc';

    rows.sort((a, b) => {
        let aVal, bVal;

        switch(column) {
            case 'name':
                aVal = a.dataset.name;
                bVal = b.dataset.name;
                break;
            case 'service_type':
                aVal = a.dataset.serviceType;
                bVal = b.dataset.serviceType;
                break;
            case 'status':
                aVal = a.dataset.status;
                bVal = b.dataset.status;
                break;
            default:
                return 0;
        }

        if (sortDirection[column] === 'asc') {
            return aVal.localeCompare(bVal);
        } else {
            return bVal.localeCompare(aVal);
        }
    });

    // Re-append sorted rows
    rows.forEach(row => tbody.appendChild(row));
}

function getSelectedProviderIds() {
    return Array.from(document.querySelectorAll('.row-checkbox-provider:checked')).map(cb => cb.value);
}

function bulkDeleteProviders() {
    const selectedIds = getSelectedProviderIds();
    if (selectedIds.length === 0) return;

    if (confirm(`Are you sure you want to delete ${selectedIds.length} selected provider(s)?\\n\\nThis action cannot be undone.`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'index.php?module=contacts&action=bulk_delete_providers';

        selectedIds.forEach(id => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'provider_ids[]';
            input.value = id;
            form.appendChild(input);
        });

        document.body.appendChild(form);
        form.submit();
    }
}

function bulkActivateProviders() {
    const selectedIds = getSelectedProviderIds();
    if (selectedIds.length === 0) return;

    if (confirm(`Activate ${selectedIds.length} selected provider(s)?`)) {
        bulkUpdateProviderStatus(selectedIds, 'Active');
    }
}

function bulkDeactivateProviders() {
    const selectedIds = getSelectedProviderIds();
    if (selectedIds.length === 0) return;

    if (confirm(`Deactivate ${selectedIds.length} selected provider(s)?`)) {
        bulkUpdateProviderStatus(selectedIds, 'Inactive');
    }
}

function bulkUpdateProviderStatus(ids, status) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = 'index.php?module=contacts&action=bulk_update_providers';

    ids.forEach(id => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'provider_ids[]';
        input.value = id;
        form.appendChild(input);
    });

    const statusInput = document.createElement('input');
    statusInput.type = 'hidden';
    statusInput.name = 'status';
    statusInput.value = status;
    form.appendChild(statusInput);

    document.body.appendChild(form);
    form.submit();
}

function viewProvider(id) {
    window.location.href = `index.php?module=contacts&action=view_provider&id=${id}`;
}

function deleteProvider(id, name) {
    if (confirm(`Are you sure you want to delete "${name}"?\\n\\nThis action cannot be undone.`)) {
        window.location.href = `index.php?module=contacts&action=delete_provider&id=${id}`;
    }
}
</script>

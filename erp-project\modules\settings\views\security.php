<div class="table-container">
    <div class="table-header">
        <h2 class="table-title">Security Settings</h2>
        <div class="table-actions">
            <button class="button">
                <span>🔒</span>
                <span>Save Security Settings</span>
            </button>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Password Policy</h3>
            <p class="card-description">Configure password requirements and security policies</p>
        </div>

        <div class="card-content">
            <form>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-sm);">
                    <div class="form-group">
                        <label for="min_password_length" class="form-label">Minimum Password Length</label>
                        <input
                            type="number"
                            id="min_password_length"
                            name="min_password_length"
                            class="form-input"
                            value="8"
                            min="6"
                            max="32"
                        >
                        <div class="form-help-text">Minimum characters required</div>
                    </div>

                    <div class="form-group">
                        <label for="password_expiry" class="form-label">Password Expiry (days)</label>
                        <input
                            type="number"
                            id="password_expiry"
                            name="password_expiry"
                            class="form-input"
                            value="90"
                            min="30"
                            max="365"
                        >
                        <div class="form-help-text">Days before password expires</div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">Password Requirements</label>
                    <div style="display: flex; flex-direction: column; gap: var(--spacing-xs);">
                        <label style="display: flex; align-items: center; gap: var(--spacing-sm); font-size: 0.875rem; margin-bottom: 0;">
                            <input type="checkbox" checked> Require uppercase letters
                        </label>
                        <label style="display: flex; align-items: center; gap: var(--spacing-sm); font-size: 0.875rem; margin-bottom: 0;">
                            <input type="checkbox" checked> Require lowercase letters
                        </label>
                        <label style="display: flex; align-items: center; gap: var(--spacing-sm); font-size: 0.875rem; margin-bottom: 0;">
                            <input type="checkbox" checked> Require numbers
                        </label>
                        <label style="display: flex; align-items: center; gap: var(--spacing-sm); font-size: 0.875rem; margin-bottom: 0;">
                            <input type="checkbox"> Require special characters
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">Session Security</label>
                    <div style="display: flex; flex-direction: column; gap: var(--spacing-xs);">
                        <label style="display: flex; align-items: center; gap: var(--spacing-sm); font-size: 0.875rem; margin-bottom: 0;">
                            <input type="checkbox" checked> Auto-logout on inactivity
                        </label>
                        <label style="display: flex; align-items: center; gap: var(--spacing-sm); font-size: 0.875rem; margin-bottom: 0;">
                            <input type="checkbox" checked> Require re-authentication for sensitive actions
                        </label>
                        <label style="display: flex; align-items: center; gap: var(--spacing-sm); font-size: 0.875rem; margin-bottom: 0;">
                            <input type="checkbox"> Enable IP address restrictions
                        </label>
                    </div>
                </div>
            </form>
        </div>

        <div class="card-footer">
            <div class="flex gap-2">
                <button type="submit" class="button">
                    <span>🔒</span>
                    <span>Save Security Settings</span>
                </button>
                <button type="button" class="button button-outline">
                    <span>🔄</span>
                    <span>Reset to Defaults</span>
                </button>
            </div>
        </div>
    </div>
</div>

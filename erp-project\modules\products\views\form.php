<div class="card">
    <div class="card-header">
        <h2 class="card-title"><?php echo isset($product) ? 'Edit Product' : 'Create New Product'; ?></h2>
        <p class="card-description">
            <?php echo isset($product) ? 'Update product information below' : 'Add a new product to your inventory'; ?>
        </p>
    </div>

    <div class="card-content">
        <form action="index.php?module=products&action=save" method="post">
            <?php if (isset($product)): ?>
                <input type="hidden" name="id" value="<?php echo $product['id']; ?>">
            <?php endif; ?>

            <div class="form-group">
                <label for="name" class="form-label">Product Name</label>
                <input
                    type="text"
                    id="name"
                    name="name"
                    class="form-input"
                    value="<?php echo isset($product) ? htmlspecialchars($product['name']) : ''; ?>"
                    placeholder="Enter product name"
                    required
                >
                <div class="form-help-text">Descriptive name for the product</div>
            </div>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-sm);">
                <div class="form-group">
                    <label for="sku" class="form-label">SKU</label>
                    <input
                        type="text"
                        id="sku"
                        name="sku"
                        class="form-input"
                        value="<?php echo isset($product) ? htmlspecialchars($product['sku']) : ''; ?>"
                        placeholder="e.g., PROD-001"
                        required
                    >
                    <div class="form-help-text">Unique product identifier</div>
                </div>

                <div class="form-group">
                    <label for="quantity" class="form-label">Quantity in Stock</label>
                    <input
                        type="number"
                        id="quantity"
                        name="quantity"
                        class="form-input"
                        value="<?php echo isset($product) ? htmlspecialchars($product['quantity']) : ''; ?>"
                        placeholder="0"
                        min="0"
                        required
                    >
                    <div class="form-help-text">Current inventory count</div>
                </div>
            </div>

            <div class="form-group">
                <label for="price" class="form-label">Price ($)</label>
                <input
                    type="number"
                    id="price"
                    name="price"
                    class="form-input"
                    step="0.01"
                    min="0"
                    value="<?php echo isset($product) ? htmlspecialchars($product['price']) : ''; ?>"
                    placeholder="0.00"
                    required
                >
                <div class="form-help-text">Selling price per unit</div>
            </div>

            <div class="card-footer">
                <div class="flex gap-2">
                    <button type="submit" class="button">
                        <span><?php echo isset($product) ? '💾' : '✨'; ?></span>
                        <span><?php echo isset($product) ? 'Update Product' : 'Create Product'; ?></span>
                    </button>
                    <a href="index.php?module=products&action=list" class="button button-outline">
                        <span>↩️</span>
                        <span>Back to Products</span>
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

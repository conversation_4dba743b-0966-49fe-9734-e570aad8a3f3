<div class="table-container">
    <div class="table-header">
        <h2 class="table-title">User Management</h2>
        <div class="table-actions">
            <div class="table-search">
                <input type="text" placeholder="Search users..." id="userSearch" onkeyup="searchTable('usersTable', 'userSearch')">
            </div>
            <a href="index.php?module=users&action=new" class="button">
                <span>✨</span>
                <span>New User</span>
            </a>
        </div>
    </div>

    <table class="data-table" id="usersTable">
        <thead>
            <tr>
                <th>ID</th>
                <th>Name</th>
                <th>Email</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php if (!empty($users)): ?>
                <?php foreach ($users as $user): ?>
                    <tr>
                        <td>
                            <span style="font-weight: 600; color: hsl(var(--muted-foreground));">
                                #<?php echo $user['id']; ?>
                            </span>
                        </td>
                        <td>
                            <div style="display: flex; align-items: center; gap: var(--spacing-md);">
                                <div style="width: 32px; height: 32px; border-radius: 50%; background: var(--gradient-primary); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 0.875rem;">
                                    <?php echo strtoupper(substr($user['name'], 0, 1)); ?>
                                </div>
                                <span style="font-weight: 500;">
                                    <?php echo htmlspecialchars($user['name']); ?>
                                </span>
                            </div>
                        </td>
                        <td>
                            <span style="color: hsl(var(--muted-foreground));">
                                <?php echo htmlspecialchars($user['email']); ?>
                            </span>
                        </td>
                        <td>
                            <div class="table-actions-cell">
                                <a href="index.php?module=users&action=edit&id=<?php echo $user['id']; ?>" class="action-button edit" title="Edit user">
                                    ✏️ Edit
                                </a>
                                <a href="index.php?module=users&action=delete&id=<?php echo $user['id']; ?>" class="action-button delete" title="Delete user" onclick="return confirm('Are you sure you want to delete this user?')">
                                    🗑️ Delete
                                </a>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php else: ?>
                <tr>
                    <td colspan="4" style="text-align: center; padding: var(--spacing-2xl); color: hsl(var(--muted-foreground));">
                        <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">👥</div>
                        <p style="font-size: 1.125rem; margin-bottom: var(--spacing-sm);">No users found</p>
                        <p style="font-size: 0.875rem;">Start by creating your first user</p>
                    </td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>

    <?php if (!empty($users)): ?>
        <div class="table-pagination">
            <div class="pagination-info">
                Showing <?php echo count($users); ?> user(s)
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
function searchTable(tableId, searchId) {
    const input = document.getElementById(searchId);
    const filter = input.value.toLowerCase();
    const table = document.getElementById(tableId);
    const rows = table.getElementsByTagName('tr');

    for (let i = 1; i < rows.length; i++) {
        const cells = rows[i].getElementsByTagName('td');
        let found = false;

        for (let j = 0; j < cells.length - 1; j++) { // Exclude actions column
            if (cells[j].textContent.toLowerCase().includes(filter)) {
                found = true;
                break;
            }
        }

        rows[i].style.display = found ? '' : 'none';
    }
}
</script>

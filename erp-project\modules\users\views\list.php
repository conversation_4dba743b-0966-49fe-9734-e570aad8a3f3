<!-- Success/Error Messages -->
<?php if (isset($_GET['success'])): ?>
    <div class="alert alert-success">
        <i data-lucide="check-circle"></i>
        <span>User added successfully!</span>
    </div>
<?php endif; ?>

<?php if (isset($_GET['updated'])): ?>
    <div class="alert alert-success">
        <i data-lucide="check-circle"></i>
        <span>
            <?php
            $count = intval($_GET['updated']);
            echo $count > 1 ? "$count users updated successfully!" : "User updated successfully!";
            ?>
        </span>
    </div>
<?php endif; ?>

<?php if (isset($_GET['deleted'])): ?>
    <div class="alert alert-success">
        <i data-lucide="check-circle"></i>
        <span>
            <?php
            $count = intval($_GET['deleted']);
            echo $count > 1 ? "$count users deleted successfully!" : "User deleted successfully!";
            ?>
        </span>
    </div>
<?php endif; ?>

<?php if (isset($_GET['error'])): ?>
    <div class="alert alert-error">
        <i data-lucide="alert-circle"></i>
        <span>An error occurred. Please try again.</span>
    </div>
<?php endif; ?>

<div class="table-container">
    <div class="table-header">
        <div class="table-title">
            <h2>User Management</h2>
            <p>Manage system users and permissions</p>
        </div>
        <div class="table-actions">
            <div id="bulkActionsUsers" style="display: none; margin-right: 8px; gap: 6px;">
                <button class="bulk-action-btn bulk-delete" onclick="bulkDeleteUsers()" title="Delete selected users">
                    <i data-lucide="trash-2"></i>
                    <span>Delete</span>
                </button>
                <button class="bulk-action-btn bulk-activate" onclick="bulkActivateUsers()" title="Activate selected users">
                    <i data-lucide="check-circle"></i>
                    <span>Activate</span>
                </button>
                <button class="bulk-action-btn bulk-deactivate" onclick="bulkDeactivateUsers()" title="Deactivate selected users">
                    <i data-lucide="pause-circle"></i>
                    <span>Deactivate</span>
                </button>
            </div>
            <div class="search-box">
                <i data-lucide="search"></i>
                <input type="text" id="userSearch" placeholder="Search users..." class="search-input">
            </div>
            <div class="filter-dropdown">
                <button class="button button-outline" id="filterBtnUsers" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px); border: 1px solid hsl(var(--border) / 0.3); box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    <i data-lucide="sliders-horizontal"></i>
                    <span>Filter</span>
                </button>
                <div class="filter-dropdown-content" id="filterDropdownUsers">
                    <div class="filter-option active" data-filter="all">All Users</div>
                    <div class="filter-option" data-filter="active">Active Only</div>
                    <div class="filter-option" data-filter="inactive">Inactive Only</div>
                    <div class="filter-option" data-filter="admin">Administrators</div>
                    <div class="filter-option" data-filter="user">Regular Users</div>
                </div>
            </div>
            <a href="index.php?module=users&action=new" class="button" style="background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary) / 0.8) 100%); color: white; box-shadow: 0 2px 8px hsl(var(--primary) / 0.3);">
                <i data-lucide="user-plus"></i>
                <span>Add User</span>
            </a>
        </div>
    </div>

    <div class="table-content">
        <?php if (empty($users)): ?>
            <div style="text-align: center; padding: var(--spacing-2xl); color: hsl(var(--muted-foreground));">
                <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">
                    <i data-lucide="users" style="width: 48px; height: 48px; stroke-width: 1;"></i>
                </div>
                <h3 style="margin-bottom: var(--spacing-sm);">No users found</h3>
                <p style="margin-bottom: var(--spacing-lg);">Start by adding your first user</p>
                <a href="index.php?module=users&action=new" class="button">
                    <i data-lucide="plus"></i>
                    <span>Add User</span>
                </a>
            </div>
        <?php else: ?>
            <table class="table" id="usersTable">
                <thead>
                    <tr>
                        <th style="width: 3%;">
                            <input type="checkbox" id="selectAllUsers" style="margin: 0;">
                        </th>
                        <th style="width: 5%;">ID</th>
                        <th style="width: 40%; cursor: pointer; user-select: none;" onclick="sortUsersTable('name')" onmouseover="this.style.background='hsl(var(--muted) / 0.1)'" onmouseout="this.style.background=''">
                            User <i data-lucide="arrow-up-down" style="width: 12px; height: 12px; display: inline; opacity: 0.6; transition: opacity 0.2s;"></i>
                        </th>
                        <th style="width: 32%; cursor: pointer; user-select: none;" onclick="sortUsersTable('email')" onmouseover="this.style.background='hsl(var(--muted) / 0.1)'" onmouseout="this.style.background=''">
                            Email <i data-lucide="arrow-up-down" style="width: 12px; height: 12px; display: inline; opacity: 0.6; transition: opacity 0.2s;"></i>
                        </th>
                        <th style="width: 20%;">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($users as $user): ?>
                        <tr class="user-row"
                            data-name="<?php echo strtolower(htmlspecialchars($user['name'])); ?>"
                            data-email="<?php echo strtolower(htmlspecialchars($user['email'])); ?>"
                            data-id="<?php echo $user['id']; ?>">
                            <td>
                                <input type="checkbox" class="row-checkbox-user" value="<?php echo $user['id']; ?>" style="margin: 0;">
                            </td>
                            <td>
                                <span style="font-weight: 600; color: hsl(var(--muted-foreground)); font-size: 0.8rem;">
                                    #<?php echo $user['id']; ?>
                                </span>
                            </td>
                            <td>
                                <div class="user-info">
                                    <div class="user-avatar">
                                        <?php echo strtoupper(substr($user['name'], 0, 1)); ?>
                                    </div>
                                    <div class="user-details">
                                        <div class="user-name"><?php echo htmlspecialchars($user['name']); ?></div>
                                        <div class="user-role">System User</div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div style="display: flex; flex-direction: column; gap: 1px;">
                                    <span style="font-weight: 500; font-size: 0.8rem;"><?php echo htmlspecialchars($user['email']); ?></span>
                                    <span style="font-size: 0.7rem; color: hsl(var(--muted-foreground));">Active Account</span>
                                </div>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <a href="index.php?module=users&action=edit&id=<?php echo $user['id']; ?>"
                                       class="button button-sm button-edit" title="Edit User">
                                        <i data-lucide="edit-3"></i>
                                    </a>
                                    <button onclick="viewUser(<?php echo $user['id']; ?>)"
                                            class="button button-sm button-view"
                                            title="View User Details">
                                        <i data-lucide="eye"></i>
                                    </button>
                                    <button onclick="deleteUser(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['name']); ?>')"
                                            class="button button-sm button-destructive"
                                            title="Delete User">
                                        <i data-lucide="trash-2"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>

    <?php if (!empty($users)): ?>
        <div class="table-footer" style="padding: 8px 12px; border-top: 1px solid hsl(var(--border) / 0.15); background: hsl(var(--muted) / 0.05); display: flex; align-items: center; justify-content: space-between;">
            <div style="font-size: 0.8rem; color: hsl(var(--muted-foreground));">
                <span id="userCount">Showing <?php echo count($users); ?> of <?php echo count($users); ?> users</span>
            </div>
            <div style="display: flex; align-items: center; gap: 4px;">
                <button class="button button-outline button-sm" disabled>
                    <i data-lucide="chevron-left"></i>
                    <span>Previous</span>
                </button>
                <button class="button button-outline button-sm" disabled>
                    <span>Next</span>
                    <i data-lucide="chevron-right"></i>
                </button>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Floating Action Button -->
<button class="fab" onclick="window.location.href='index.php?module=users&action=new'" title="Add New User">
    <i data-lucide="plus"></i>
</button>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('userSearch');
    const filterBtn = document.getElementById('filterBtnUsers');
    const filterDropdown = document.getElementById('filterDropdownUsers');
    const filterOptions = document.querySelectorAll('#filterDropdownUsers .filter-option');
    const userRows = document.querySelectorAll('.user-row');
    const userCount = document.getElementById('userCount');
    const selectAllCheckbox = document.getElementById('selectAllUsers');
    const rowCheckboxes = document.querySelectorAll('.row-checkbox-user');
    const bulkActions = document.getElementById('bulkActionsUsers');
    const totalUsers = userRows.length;

    let currentFilter = 'all';
    let sortDirection = {};

    // Search functionality
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            filterUsers();
        });
    }

    // Filter dropdown toggle
    if (filterBtn) {
        filterBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            filterDropdown.classList.toggle('show');
        });
    }

    // Close dropdown when clicking outside
    document.addEventListener('click', function() {
        if (filterDropdown) {
            filterDropdown.classList.remove('show');
        }
    });

    // Filter options
    filterOptions.forEach(option => {
        option.addEventListener('click', function() {
            filterOptions.forEach(opt => opt.classList.remove('active'));
            this.classList.add('active');
            currentFilter = this.dataset.filter;
            filterBtn.querySelector('span').textContent = this.textContent;
            filterDropdown.classList.remove('show');
            filterUsers();
        });
    });

    // Select all functionality
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const isChecked = this.checked;
            rowCheckboxes.forEach(checkbox => {
                if (checkbox.closest('.user-row').style.display !== 'none') {
                    checkbox.checked = isChecked;
                }
            });
            updateBulkActions();
        });
    }

    // Individual checkbox functionality
    rowCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateBulkActions();
            updateSelectAll();
        });
    });

    function updateBulkActions() {
        if (!bulkActions) return;
        const checkedBoxes = Array.from(rowCheckboxes).filter(cb =>
            cb.checked && cb.closest('.user-row').style.display !== 'none'
        );
        bulkActions.style.display = checkedBoxes.length > 0 ? 'flex' : 'none';
    }

    function updateSelectAll() {
        if (!selectAllCheckbox) return;
        const visibleCheckboxes = Array.from(rowCheckboxes).filter(cb =>
            cb.closest('.user-row').style.display !== 'none'
        );
        const checkedVisible = visibleCheckboxes.filter(cb => cb.checked);

        selectAllCheckbox.checked = visibleCheckboxes.length > 0 &&
                                   checkedVisible.length === visibleCheckboxes.length;
        selectAllCheckbox.indeterminate = checkedVisible.length > 0 &&
                                         checkedVisible.length < visibleCheckboxes.length;
    }

    function filterUsers() {
        if (!searchInput || !userCount) return;
        const searchTerm = searchInput.value.toLowerCase();
        let visibleCount = 0;

        userRows.forEach(row => {
            const name = row.dataset.name;
            const email = row.dataset.email;

            const searchMatch = name.includes(searchTerm) || email.includes(searchTerm);

            let filterMatch = true;
            if (currentFilter !== 'all') {
                // Add filter logic here when needed
                filterMatch = true;
            }

            if (searchMatch && filterMatch) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
                const checkbox = row.querySelector('.row-checkbox-user');
                if (checkbox) checkbox.checked = false;
            }
        });

        userCount.textContent = `Showing ${visibleCount} of ${totalUsers} users`;
        updateBulkActions();
        updateSelectAll();
    }
});

function sortUsersTable(column) {
    const table = document.getElementById('usersTable');
    if (!table) return;

    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('.user-row'));

    // Toggle sort direction
    let sortDirection = {};
    sortDirection[column] = sortDirection[column] === 'asc' ? 'desc' : 'asc';

    rows.sort((a, b) => {
        let aVal, bVal;

        switch(column) {
            case 'name':
                aVal = a.dataset.name;
                bVal = b.dataset.name;
                break;
            case 'email':
                aVal = a.dataset.email;
                bVal = b.dataset.email;
                break;
            default:
                return 0;
        }

        if (sortDirection[column] === 'asc') {
            return aVal.localeCompare(bVal);
        } else {
            return bVal.localeCompare(aVal);
        }
    });

    // Re-append sorted rows
    rows.forEach(row => tbody.appendChild(row));
}

function getSelectedUserIds() {
    return Array.from(document.querySelectorAll('.row-checkbox-user:checked')).map(cb => cb.value);
}

function bulkDeleteUsers() {
    const selectedIds = getSelectedUserIds();
    if (selectedIds.length === 0) return;

    if (confirm(`Are you sure you want to delete ${selectedIds.length} selected user(s)?\\n\\nThis action cannot be undone.`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'index.php?module=users&action=bulk_delete';

        selectedIds.forEach(id => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'user_ids[]';
            input.value = id;
            form.appendChild(input);
        });

        document.body.appendChild(form);
        form.submit();
    }
}

function bulkActivateUsers() {
    const selectedIds = getSelectedUserIds();
    if (selectedIds.length === 0) return;

    if (confirm(`Activate ${selectedIds.length} selected user(s)?`)) {
        bulkUpdateUserStatus(selectedIds, 'Active');
    }
}

function bulkDeactivateUsers() {
    const selectedIds = getSelectedUserIds();
    if (selectedIds.length === 0) return;

    if (confirm(`Deactivate ${selectedIds.length} selected user(s)?`)) {
        bulkUpdateUserStatus(selectedIds, 'Inactive');
    }
}

function bulkUpdateUserStatus(ids, status) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = 'index.php?module=users&action=bulk_update';

    ids.forEach(id => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'user_ids[]';
        input.value = id;
        form.appendChild(input);
    });

    const statusInput = document.createElement('input');
    statusInput.type = 'hidden';
    statusInput.name = 'status';
    statusInput.value = status;
    form.appendChild(statusInput);

    document.body.appendChild(form);
    form.submit();
}

function viewUser(id) {
    window.location.href = `index.php?module=users&action=view&id=${id}`;
}

function deleteUser(id, name) {
    if (confirm(`Are you sure you want to delete "${name}"?\\n\\nThis action cannot be undone.`)) {
        window.location.href = `index.php?module=users&action=delete&id=${id}`;
    }
}
</script>

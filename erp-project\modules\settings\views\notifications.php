<div class="table-container">
    <div class="table-header">
        <h2 class="table-title">Notification Settings</h2>
        <div class="table-actions">
            <button class="button">
                <span>🔔</span>
                <span>Save Notification Settings</span>
            </button>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Email Notifications</h3>
            <p class="card-description">Configure when and how email notifications are sent</p>
        </div>

        <div class="card-content">
            <form>
                <div class="form-group">
                    <label for="smtp_server" class="form-label">SMTP Server</label>
                    <input
                        type="text"
                        id="smtp_server"
                        name="smtp_server"
                        class="form-input"
                        value="smtp.gmail.com"
                        placeholder="smtp.example.com"
                    >
                    <div class="form-help-text">SMTP server for sending emails</div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-sm);">
                    <div class="form-group">
                        <label for="smtp_port" class="form-label">SMTP Port</label>
                        <input
                            type="number"
                            id="smtp_port"
                            name="smtp_port"
                            class="form-input"
                            value="587"
                        >
                        <div class="form-help-text">Usually 587 or 465</div>
                    </div>

                    <div class="form-group">
                        <label for="smtp_encryption" class="form-label">Encryption</label>
                        <select id="smtp_encryption" name="smtp_encryption" class="form-input">
                            <option value="tls" selected>TLS</option>
                            <option value="ssl">SSL</option>
                            <option value="none">None</option>
                        </select>
                        <div class="form-help-text">Email encryption method</div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">Notification Types</label>
                    <div style="display: flex; flex-direction: column; gap: var(--spacing-xs);">
                        <label style="display: flex; align-items: center; gap: var(--spacing-sm); font-size: 0.875rem; margin-bottom: 0;">
                            <input type="checkbox" checked> New user registrations
                        </label>
                        <label style="display: flex; align-items: center; gap: var(--spacing-sm); font-size: 0.875rem; margin-bottom: 0;">
                            <input type="checkbox" checked> New orders
                        </label>
                        <label style="display: flex; align-items: center; gap: var(--spacing-sm); font-size: 0.875rem; margin-bottom: 0;">
                            <input type="checkbox" checked> Low inventory alerts
                        </label>
                        <label style="display: flex; align-items: center; gap: var(--spacing-sm); font-size: 0.875rem; margin-bottom: 0;">
                            <input type="checkbox"> System maintenance alerts
                        </label>
                        <label style="display: flex; align-items: center; gap: var(--spacing-sm); font-size: 0.875rem; margin-bottom: 0;">
                            <input type="checkbox" checked> Security alerts
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <label for="notification_frequency" class="form-label">Digest Frequency</label>
                    <select id="notification_frequency" name="notification_frequency" class="form-input">
                        <option value="immediate" selected>Immediate</option>
                        <option value="hourly">Hourly</option>
                        <option value="daily">Daily</option>
                        <option value="weekly">Weekly</option>
                    </select>
                    <div class="form-help-text">How often to send notification digests</div>
                </div>
            </form>
        </div>

        <div class="card-footer">
            <div class="flex gap-2">
                <button type="submit" class="button">
                    <span>🔔</span>
                    <span>Save Notification Settings</span>
                </button>
                <button type="button" class="button button-outline">
                    <span>📧</span>
                    <span>Test Email</span>
                </button>
            </div>
        </div>
    </div>
</div>

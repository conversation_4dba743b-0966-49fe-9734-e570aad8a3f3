<div class="table-container">
    <div class="table-header">
        <h2 class="table-title">Client Management</h2>
        <div class="table-actions">
            <div class="table-search">
                <input type="text" placeholder="Search clients..." id="clientSearch" onkeyup="searchTable('clientsTable', 'clientSearch')">
            </div>
            <a href="index.php?module=clients&action=new" class="button">
                <span>✨</span>
                <span>New Client</span>
            </a>
        </div>
    </div>

    <table class="data-table" id="clientsTable">
        <thead>
            <tr>
                <th>ID</th>
                <th>Client</th>
                <th>Contact</th>
                <th>Address</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php if (!empty($clients)): ?>
                <?php foreach ($clients as $client): ?>
                    <tr>
                        <td>
                            <span style="font-weight: 600; color: hsl(var(--muted-foreground));">
                                #<?php echo $client['id']; ?>
                            </span>
                        </td>
                        <td>
                            <div style="display: flex; align-items: center; gap: var(--spacing-md);">
                                <div style="width: 32px; height: 32px; border-radius: 50%; background: var(--gradient-primary); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 0.875rem;">
                                    <?php echo strtoupper(substr($client['name'], 0, 1)); ?>
                                </div>
                                <span style="font-weight: 500;">
                                    <?php echo htmlspecialchars($client['name']); ?>
                                </span>
                            </div>
                        </td>
                        <td>
                            <div>
                                <div style="color: hsl(var(--foreground)); font-weight: 500;">
                                    <?php echo htmlspecialchars($client['email']); ?>
                                </div>
                                <?php if (!empty($client['phone'])): ?>
                                    <div style="color: hsl(var(--muted-foreground)); font-size: 0.875rem;">
                                        📞 <?php echo htmlspecialchars($client['phone']); ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td>
                            <span style="color: hsl(var(--muted-foreground)); font-size: 0.875rem;">
                                <?php echo !empty($client['address']) ? htmlspecialchars($client['address']) : 'No address'; ?>
                            </span>
                        </td>
                        <td>
                            <div class="table-actions-cell">
                                <a href="index.php?module=clients&action=edit&id=<?php echo $client['id']; ?>" class="action-button edit" title="Edit client">
                                    ✏️ Edit
                                </a>
                                <a href="index.php?module=clients&action=delete&id=<?php echo $client['id']; ?>" class="action-button delete" title="Delete client" onclick="return confirm('Are you sure you want to delete this client?')">
                                    🗑️ Delete
                                </a>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php else: ?>
                <tr>
                    <td colspan="5" style="text-align: center; padding: var(--spacing-2xl); color: hsl(var(--muted-foreground));">
                        <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">👤</div>
                        <p style="font-size: 1.125rem; margin-bottom: var(--spacing-sm);">No clients found</p>
                        <p style="font-size: 0.875rem;">Start by adding your first client</p>
                    </td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>

    <?php if (!empty($clients)): ?>
        <div class="table-pagination">
            <div class="pagination-info">
                Showing <?php echo count($clients); ?> client(s)
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
function searchTable(tableId, searchId) {
    const input = document.getElementById(searchId);
    const filter = input.value.toLowerCase();
    const table = document.getElementById(tableId);
    const rows = table.getElementsByTagName('tr');

    for (let i = 1; i < rows.length; i++) {
        const cells = rows[i].getElementsByTagName('td');
        let found = false;

        for (let j = 0; j < cells.length - 1; j++) { // Exclude actions column
            if (cells[j].textContent.toLowerCase().includes(filter)) {
                found = true;
                break;
            }
        }

        rows[i].style.display = found ? '' : 'none';
    }
}
</script>

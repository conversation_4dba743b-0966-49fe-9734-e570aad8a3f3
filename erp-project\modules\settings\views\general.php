<div class="table-container">
    <div class="table-header">
        <h2 class="table-title">General Settings</h2>
        <div class="table-actions">
            <button class="button">
                <span>💾</span>
                <span>Save Settings</span>
            </button>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h3 class="card-title">System Configuration</h3>
            <p class="card-description">Configure general system settings and preferences</p>
        </div>

        <div class="card-content">
            <form>
                <div class="form-group">
                    <label for="company_name" class="form-label">Company Name</label>
                    <input
                        type="text"
                        id="company_name"
                        name="company_name"
                        class="form-input"
                        value="ERP System"
                        placeholder="Enter company name"
                    >
                    <div class="form-help-text">This will appear in headers and reports</div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-sm);">
                    <div class="form-group">
                        <label for="timezone" class="form-label">Timezone</label>
                        <select id="timezone" name="timezone" class="form-input">
                            <option value="UTC">UTC</option>
                            <option value="America/New_York">Eastern Time</option>
                            <option value="America/Chicago">Central Time</option>
                            <option value="America/Denver">Mountain Time</option>
                            <option value="America/Los_Angeles" selected>Pacific Time</option>
                        </select>
                        <div class="form-help-text">Default timezone for the system</div>
                    </div>

                    <div class="form-group">
                        <label for="currency" class="form-label">Default Currency</label>
                        <select id="currency" name="currency" class="form-input">
                            <option value="USD" selected>USD ($)</option>
                            <option value="EUR">EUR (€)</option>
                            <option value="GBP">GBP (£)</option>
                            <option value="CAD">CAD (C$)</option>
                        </select>
                        <div class="form-help-text">Currency for financial calculations</div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="date_format" class="form-label">Date Format</label>
                    <select id="date_format" name="date_format" class="form-input">
                        <option value="Y-m-d" selected>YYYY-MM-DD</option>
                        <option value="m/d/Y">MM/DD/YYYY</option>
                        <option value="d/m/Y">DD/MM/YYYY</option>
                        <option value="d-m-Y">DD-MM-YYYY</option>
                    </select>
                    <div class="form-help-text">How dates are displayed throughout the system</div>
                </div>

                <div class="form-group">
                    <label class="form-label">System Features</label>
                    <div style="display: flex; flex-direction: column; gap: var(--spacing-xs);">
                        <label style="display: flex; align-items: center; gap: var(--spacing-sm); font-size: 0.875rem; margin-bottom: 0;">
                            <input type="checkbox" checked> Enable email notifications
                        </label>
                        <label style="display: flex; align-items: center; gap: var(--spacing-sm); font-size: 0.875rem; margin-bottom: 0;">
                            <input type="checkbox" checked> Enable audit logging
                        </label>
                        <label style="display: flex; align-items: center; gap: var(--spacing-sm); font-size: 0.875rem; margin-bottom: 0;">
                            <input type="checkbox"> Enable two-factor authentication
                        </label>
                        <label style="display: flex; align-items: center; gap: var(--spacing-sm); font-size: 0.875rem; margin-bottom: 0;">
                            <input type="checkbox" checked> Enable automatic backups
                        </label>
                    </div>
                </div>
            </form>
        </div>

        <div class="card-footer">
            <div class="flex gap-2">
                <button type="submit" class="button">
                    <span>💾</span>
                    <span>Save Changes</span>
                </button>
                <button type="button" class="button button-outline">
                    <span>🔄</span>
                    <span>Reset to Defaults</span>
                </button>
            </div>
        </div>
    </div>
</div>

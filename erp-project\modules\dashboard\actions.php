<?php

// modules/dashboard/actions.php

function index_action() {
    $pdo = db_connect();

    // Get statistics
    $stats = [];

    try {
        // Count users
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
        $stats['total_users'] = $stmt->fetch()['count'];

        // Count clients
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM clients");
        $stats['total_clients'] = $stmt->fetch()['count'];

        // Count products
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM products");
        $stats['total_products'] = $stmt->fetch()['count'];

        // Calculate total sales
        $stmt = $pdo->query("SELECT SUM(total) as total FROM sales");
        $result = $stmt->fetch();
        $stats['total_sales'] = $result['total'] ?? 0;

    } catch (Exception $e) {
        // If tables don't exist yet, set defaults
        $stats = [
            'total_users' => 0,
            'total_clients' => 0,
            'total_products' => 0,
            'total_sales' => 0
        ];
    }

    // Get recent activity (mock data for now)
    $recent_activity = [
        [
            'type' => 'User',
            'description' => 'New user registered',
            'time' => '2 hours ago'
        ],
        [
            'type' => 'Sale',
            'description' => 'New sale completed',
            'time' => '4 hours ago'
        ],
        [
            'type' => 'Product',
            'description' => 'Product inventory updated',
            'time' => '6 hours ago'
        ],
        [
            'type' => 'Client',
            'description' => 'New client added',
            'time' => '1 day ago'
        ]
    ];

    render('modules/dashboard/views/index', [
        'stats' => $stats,
        'recent_activity' => $recent_activity
    ]);
}
